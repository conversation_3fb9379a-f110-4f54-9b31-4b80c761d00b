import request from '@/config/axios'

// ==================== 基础类型定义 ====================

// 订单基础信息
export interface HousekeepingServiceOrder {
  id?: string
  orderNumber?: string
  customerName: string
  customerPhone: string
  serviceAddress: string
  servicePackage?: string
  serviceType: string
  serviceStartDate: string
  serviceEndDate?: string
  serviceDuration?: string
  serviceFrequency?: string
  unitPrice: number
  totalAmount: number
  discountAmount?: number
  actualAmount: number
  practitionerOneid?: string
  agencyId?: number
  remark?: string
  orderStatus?: string
  paymentStatus?: string
  appointmentTime?: string
  createTime?: string
  updateTime?: string
}

// 后端实际返回的订单数据结构
export interface BackendOrderData {
  id: string
  orderNumber: string
  customerName: string
  customerPhone: string
  serviceAddress: string
  servicePackage: string
  serviceType: string
  serviceStartDate: string
  serviceEndDate: string
  serviceDuration: string
  serviceFrequency: string
  unitPrice: number
  totalAmount: number
  discountAmount: number
  actualAmount: number
  practitionerOneid: string
  agencyId: number
  remark: string
  orderStatus: string
  paymentStatus: string
  appointmentTime: string
  createTime: string
  updateTime: string
  // 扩展字段
  servicePersonnel?: string
  serviceAgency?: string
  completedTasks?: number
  totalTasks?: number
}

// 后端分页查询响应结构
export interface BackendPageResponse {
  code: number
  data: {
    total: number
    pages: number
    current: number
    size: number
    records: BackendOrderData[]
  }
  message: string
  timestamp: string
}

// 新增订单请求参数
export interface AddOrderParams {
  businessOpportunity?: string
  lead?: string
  customerName: string
  customerPhone: string
  serviceAddress: string
  servicePackage: string
  serviceType: string
  serviceStartDate: string
  serviceEndDate?: string
  serviceDuration?: string
  serviceFrequency?: string
  unitPrice: number
  totalAmount: number
  discountAmount?: number
  actualAmount: number
  practitionerOneid?: string
  agencyId?: number
  remark?: string
  // 支付相关信息
  paymentStatus?: string
  paymentMethod?: string
  receivedAmount?: number
  paymentTime?: string
  paymentRemark?: string
}

// 新增订单响应结果
export interface AddOrderResult {
  orderId: string
  orderNumber: string
}

// 更新订单请求参数
export interface UpdateOrderParams {
  id: string
  customerName: string
  customerPhone: string
  serviceAddress: string
  serviceStartDate: string
  unitPrice: number
  totalAmount: number
  actualAmount: number
  paymentStatus: string
  paymentMethod?: string
  receivedAmount?: number
  paymentTime?: string
  paymentRemark?: string
  agencyId?: number
  agencyName?: string
  practitionerOneid?: string
  practitionerName?: string
  practitionerPhone?: string
  remark?: string
}

// 删除订单请求参数
export interface DeleteOrderParams {
  id: string
}

// 分页查询参数
export interface OrderPageParams {
  page: number
  size: number
  orderStatus?: string
  paymentStatus?: string
  serviceType?: string
  keyword?: string
  startDate?: string
  endDate?: string
}

// 分页查询结果
export interface OrderPageResult {
  records: HousekeepingServiceOrder[]
  total: number
  size: number
  current: number
  pages: number
}

// ==================== 订单详情响应 ====================

// 订单详情返回结果
export interface OrderDetailResult {
  orderInfo: {
    id: number
    orderNumber: string
    orderStatus: string
    paymentStatus: string
    opportunityId: string
    leadId: string
    totalAmount: number
    servicePackage: string
    serviceAddress: string
    practitionerOneid: string
    createTime: string
    updateTime: string
  }
  customerInfo: {
    customerName: string
    customerPhone: string
    serviceAddress: string
    customerRemark: string
  }
  serviceInfo: {
    serviceType: string
    servicePackage: string
    serviceStartDate: string
    serviceEndDate: string
    serviceDuration: string
    serviceAmount: number
    appointmentTime: string
    serviceFrequency: string
    serviceDescription: string
  }
  paymentInfo: {
    totalAmount: number
    paidAmount: number
    paymentMethod: string
    receivedAmount: number
    paymentTime: string
    discountAmount: number
    actualAmount: number
    paymentRemark: string
  }
  taskInfo: {
    totalTasks: number
    completedTasks: number
    taskProgress: number
  }
  practitionerInfo: {
    practitionerOneid: string
    practitionerName: string
    practitionerPhone: string
    rating: number
    experienceYears: number
  }
  agencyInfo: {
    agencyId: number
    agencyCode: string
    agencyName: string
    agencyType: string
    cooperationStatus: string
    contactPerson: string
    contactPhone: string
  }
}

// ==================== 服务任务管理类型定义 ====================

// 任务信息
export interface ServiceTask {
  taskId: string
  taskSequence: string
  plannedDate: string
  plannedContent: string
  taskStatus: string
  currentPersonnel: string
  finalPersonnel: string
  completionTime: string
  punchLocation: string
  completionCertificate: string
}

// 分页查询任务参数
export interface TaskPageParams {
  orderId: string
  page: number
  size: number
  taskStatus?: string
  executor?: string
  dateRange?: string[]
}

// 完成任务请求参数
export interface CompleteTaskParams {
  taskId: string
  finalPersonnel: string
  completionTime: string
  punchLocation: string
  certificateFile: File
  remarks?: string
}

// 指派任务请求参数
export interface AssignTaskParams {
  taskId: string
  practitionerOneid: string
  practitionerName: string
  practitionerPhone: string
  plannedStartTime: string
  plannedEndTime: string
  remarks?: string
}

// 取消任务请求参数
export interface CancelTaskParams {
  taskId: string
  cancelReason: string
  remarks?: string
}

// 编辑任务请求参数
export interface EditTaskParams {
  taskId: string
  taskName?: string
  taskDescription?: string
  plannedStartTime?: string
  plannedEndTime?: string
  practitionerOneid?: string
  remarks?: string
}

// 批量重指派请求参数
export interface BatchReassignParams {
  taskIds: string[]
  practitionerOneid: string
  practitionerName: string
  practitionerPhone: string
  reassignReason: string
  remarks?: string
}

// 完成凭证信息
export interface CertificateInfo {
  certificateUrl: string
  certificateType: string
  uploadTime: string
  uploadPerson: string
}

// ==================== 收款信息管理类型定义 ====================

// 收款信息
export interface PaymentInfo {
  paymentId?: string
  orderAmount: number
  paymentMethod: string
  paymentNotes: string
  paymentStatus: string
  paymentDate: string
  receivedAmount: number
  operator: string
}

// 支付记录
export interface PaymentRecord {
  id: string
  paymentNo: string
  paymentType: string
  paymentAmount: number
  paymentStatus: string
  paymentTime: string
  paymentRemark: string
  operatorName: string
  createTime: string
}

// 新增收款信息请求参数
export interface AddPaymentParams {
  orderId: string
  paymentType: string
  paymentAmount: number
  paymentTime?: string
  paymentRemark?: string
}

// 修改收款信息请求参数
export interface UpdatePaymentParams {
  paymentId: string
  paymentType: string
  paymentAmount: number
  paymentTime?: string
  paymentRemark?: string
}

// ==================== 收支记录管理类型定义 ====================

// 收支记录
export interface IncomeExpenseRecord {
  id: string
  type: string
  amount: number
  date: string
  description: string
}

// 新增收支记录请求参数
export interface AddIncomeExpenseParams {
  orderId: string
  type: string
  amount: number
  date: string
  description: string
}

// 修改收支记录请求参数
export interface UpdateIncomeExpenseParams {
  recordId: string
  type?: string
  amount?: number
  date?: string
  description?: string
}

// 删除收支记录请求参数
export interface DeleteIncomeExpenseParams {
  recordId: string
}

// ==================== 服务评价管理类型定义 ====================

// 服务评价信息
export interface ServiceEvaluation {
  overallRating: number
  tags: string[]
  comment: string
  evaluationTime: string
}

// 新增服务评价请求参数
export interface AddEvaluationParams {
  orderId: string
  overallRating: number
  tags?: string[]
  comment?: string
  evaluationTime: string
}

// 修改服务评价请求参数
export interface UpdateEvaluationParams {
  evaluationId: string
  overallRating?: number
  tags?: string[]
  comment?: string
}

// 删除服务评价请求参数
export interface DeleteEvaluationParams {
  evaluationId: string
}

// ==================== 售后记录管理类型定义 ====================

// 售后记录
export interface AfterSalesRecord {
  id: string
  workOrderType: string
  problemDescription: string
  workOrderStatus: string
  processingResult: string
  createTime: string
}

// 新增售后记录请求参数
export interface AddAfterSalesParams {
  orderId: string
  workOrderType: string
  problemDescription: string
  workOrderStatus: string
  processingResult?: string
  createTime: string
}

// 修改售后记录请求参数
export interface UpdateAfterSalesParams {
  afterSalesId: string
  workOrderType?: string
  problemDescription?: string
  workOrderStatus?: string
  processingResult?: string
}

// 删除售后记录请求参数
export interface DeleteAfterSalesParams {
  afterSalesId: string
}

// ==================== 操作日志管理类型定义 ====================

// 操作日志记录
export interface OperationLogRecord {
  id: string
  logType: string
  logTitle: string
  logContent: string
  oldStatus: string
  newStatus: string
  operatorName: string
  operatorRole: string
  createTime: string
}

// 操作日志查询参数
export interface OperationLogParams {
  orderNo: string
  page: number
  size: number
  logType?: string
  startDate?: string
  endDate?: string
}

// 操作日志分页响应
export interface OperationLogPageResult {
  list: OperationLogRecord[]
  total: number
  code?: number
  msg?: string
}

// ==================== 人员变动管理类型定义 ====================

// 人员变动记录
export interface PersonnelChangeRecord {
  id: string
  changeType: string
  oldPersonnel: string
  newPersonnel: string
  changeReason: string
  changeTime: string
  operator: string
}

// 人员变动查询参数
export interface PersonnelChangeParams {
  orderId: string
}

// ==================== 基础数据查询类型定义 ====================

// 服务套餐
export interface ServicePackage {
  id: string
  name: string
  category: string
  price: number
  originalPrice: number
  unit: string
  serviceDuration: string
  status: string
}

// 服务机构
export interface ServiceAgency {
  id: number
  agencyName: string
  agencyType: string
  cooperationStatus: string
  contactPerson: string
  contactPhone: string
}

// 服务人员
export interface ServicePersonnel {
  auntOneid?: string
  oneid?: string
  id?: string | number
  practitionerId?: string | number
  name: string
  phone: string
  serviceType: string
  experienceYears: number
  rating: number
  agencyName: string
  currentStatus: string
}

// 客户信息
export interface CustomerInfo {
  customerOneid: string
  customerName: string
  customerPhone: string
  customerAddress: string
  totalOrders: number
  totalAmount: number
}

// ==================== 统计信息类型定义 ====================

// 统计信息
export interface StatisticsInfo {
  totalOrders: number
  pendingOrders: number
  monthlyAmount: number
  completionRate: number
}

// ==================== 下拉数据类型定义 ====================

// 商机选项
export interface BusinessOption {
  id: number
  name: string
  customerName: string
  businessType: string
  totalPrice: number
  businessStage: string
  ownerUserName: string
}

// 线索选项
export interface LeadOption {
  id: number
  leadId: string
  customerName: string
  customerPhone: string
  businessModule: string
  leadSource: string
  leadStatus: string
}

// ==================== API接口实现 ====================

export const HousekeepingServiceOrderApi = {
  // ==================== 订单管理接口 ====================

  // 分页查询订单列表
  getOrderPage: async (params: OrderPageParams): Promise<OrderPageResult> => {
    console.log('=== 调用家政服务订单分页查询API ===')
    console.log('查询参数:', params)

    const response = await request.post({
      url: '/publicbiz/domestic-task-order/page',
      data: params
    })

    console.log('=== 后端返回的原始数据 ===')
    console.log('完整响应:', response)

    // 转换后端数据结构为前端期望的格式
    let backendData: any = null
    let dataList: any[] = []
    let totalCount: number = 0

    if (response && response.data && response.data.records) {
      // 数据在 response.data 中
      backendData = response.data
      dataList = response.data.records
      totalCount = response.data.total
      console.log('=== 数据在 response.data 中 ===')
    } else if (response && response.data && response.data.list) {
      // 数据在 response.data.list 中（后端返回格式）
      backendData = response.data
      dataList = response.data.list
      totalCount = response.data.total
      console.log('=== 数据在 response.data.list 中 ===')
    } else if (response && response.records) {
      // 数据直接在 response 中
      backendData = response
      dataList = response.records
      totalCount = response.total
      console.log('=== 数据直接在 response 中 ===')
    } else if (response && response.list) {
      // 数据直接在 response.list 中
      backendData = response
      dataList = response.list
      totalCount = response.total
      console.log('=== 数据直接在 response.list 中 ===')
    }

    if (backendData && dataList && dataList.length > 0) {
      console.log('=== 后端数据结构 ===')
      console.log('后端数据:', backendData)
      console.log('数据列表长度:', dataList.length)

      const convertedRecords = dataList.map((backendOrder) => {
        const converted = {
          id: backendOrder.id || '',
          orderNumber: backendOrder.orderNumber || backendOrder.orderNo || '',
          customerName: backendOrder.customerName || '',
          customerPhone: backendOrder.customerPhone || '',
          serviceAddress: backendOrder.serviceAddress || '',
          servicePackage: backendOrder.servicePackage || '',
          serviceType: backendOrder.serviceType || '',
          serviceStartDate: backendOrder.serviceStartDate || '',
          serviceEndDate: backendOrder.serviceEndDate || '',
          serviceDuration: backendOrder.serviceDuration || '',
          serviceFrequency: backendOrder.serviceFrequency || '',
          unitPrice: backendOrder.unitPrice || 0,
          totalAmount: backendOrder.totalAmount || 0,
          discountAmount: backendOrder.discountAmount || 0,
          actualAmount: backendOrder.actualAmount || 0,
          practitionerOneid: backendOrder.practitionerOneid || '',
          agencyId: backendOrder.agencyId || 0,
          remark: backendOrder.remark || '',
          orderStatus: backendOrder.orderStatus || 'pending',
          paymentStatus: backendOrder.paymentStatus || 'unpaid',
          appointmentTime: backendOrder.appointmentTime || '',
          createTime: backendOrder.createTime || '',
          updateTime: backendOrder.updateTime || '',
          // 扩展字段
          servicePersonnel: backendOrder.practitionerName || backendOrder.servicePersonnel || '',
          serviceAgency: backendOrder.agencyName || backendOrder.serviceAgency || '',
          completedTasks: backendOrder.completedTasks || 0,
          totalTasks: backendOrder.totalTasks || 0
        }
        console.log('转换后的订单数据:', converted)
        return converted
      }) as HousekeepingServiceOrder[]

      const result = {
        records: convertedRecords,
        total: totalCount,
        size: params.size,
        current: params.page,
        pages: Math.ceil(totalCount / params.size)
      }

      console.log('=== 转换后的结果 ===')
      console.log('最终结果:', result)
      return result
    }

    console.log('=== 数据转换失败，返回空结果 ===')
    // 如果转换失败，返回空结果
    return {
      records: [],
      total: 0,
      size: params.size,
      current: params.page,
      pages: 0
    }
  },

  // 新增订单
  addOrder: async (data: AddOrderParams): Promise<AddOrderResult> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/add',
      data
    })
  },

  // 更新订单
  updateOrder: async (data: UpdateOrderParams): Promise<void> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/update',
      data
    })
  },

  // 删除订单
  deleteOrder: async (data: DeleteOrderParams): Promise<void> => {
    return await request.delete({
      url: `/publicbiz/domestic-task-order/${data.id}`
    })
  },

  // 获取订单详情
  getOrderDetail: async (id: string): Promise<OrderDetailResult> => {
    return await request.get({
      url: `/publicbiz/domestic-task-order/detail/${id}`
    })
  },

  // 导出订单列表
  exportOrders: async (params: {
    orderStatus?: string
    paymentStatus?: string
    serviceType?: string
    keyword?: string
    startDate?: string
    endDate?: string
    format?: string
  }): Promise<Blob> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/export',
      data: params,
      responseType: 'blob'
    })
  },

  // ==================== 服务任务管理接口 ====================

  // 分页查询任务列表
  getTaskPage: async (
    params: TaskPageParams
  ): Promise<{
    total: number
    records: ServiceTask[]
  }> => {
    return await request.get({
      url: '/publicbiz/domestic-task-order/task/page',
      params
    })
  },

  // 完成任务
  completeTask: async (data: CompleteTaskParams): Promise<void> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/task/complete',
      data
    })
  },

  // 指派任务
  assignTask: async (data: AssignTaskParams): Promise<void> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/task/assign',
      data
    })
  },

  // 取消任务
  cancelTask: async (data: CancelTaskParams): Promise<void> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/task/cancel',
      data
    })
  },

  // 编辑任务
  editTask: async (data: EditTaskParams): Promise<void> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/task/edit',
      data
    })
  },

  // 查看完成凭证
  getTaskCertificate: async (taskId: string): Promise<CertificateInfo> => {
    return await request.get({
      url: '/publicbiz/domestic-task-order/task/certificate',
      params: { taskId }
    })
  },

  // 批量重指派
  batchReassign: async (
    data: BatchReassignParams
  ): Promise<{
    successCount: number
    failCount: number
  }> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/task/batch-reassign',
      data
    })
  },

  // ==================== 收款信息管理接口 ====================

  // 查询收款信息
  getPaymentInfo: async (
    orderId: string
  ): Promise<{
    orderAmount: number
    paymentMethod: string
    paymentNotes: string
    paymentStatus: string
    paymentDate: string
    receivedAmount: number
    operator: string
  }> => {
    return await request.get({
      url: '/publicbiz/domestic-task-order/payment/info',
      params: { orderId }
    })
  },

  // 新增收款信息
  addPaymentInfo: async (data: {
    orderId: string
    paymentType: string
    paymentAmount: number
    paymentTime: string
    paymentRemark?: string
    transactionId?: string
  }): Promise<{ paymentId: string }> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/payment/add',
      data
    })
  },

  // 修改收款信息
  updatePaymentInfo: async (data: {
    paymentId: string
    paymentType?: string
    paymentAmount?: number
    paymentTime?: string
    paymentRemark?: string
    transactionId?: string
  }): Promise<boolean> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/payment/update',
      data
    })
  },

  // ==================== 收支记录管理接口 ====================

  // 查询收支记录
  getIncomeExpenseList: async (
    orderId: string
  ): Promise<{
    records: Array<{
      id: string
      type: string
      amount: number
      date: string
      description: string
    }>
  }> => {
    return await request.get({
      url: '/publicbiz/domestic-task-order/income-expense/list',
      params: { orderId }
    })
  },

  // 新增收支记录
  addIncomeExpenseRecord: async (data: {
    orderId: string
    recordType: string
    amount: string
    description: string
  }): Promise<{ recordId: string }> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/income-expense/add',
      data
    })
  },

  // 修改收支记录
  updateIncomeExpenseRecord: async (data: {
    recordId: string
    recordType?: string
    amount?: string
    description?: string
  }): Promise<boolean> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/income-expense/update',
      data
    })
  },

  // 删除收支记录
  deleteIncomeExpenseRecord: async (recordId: string): Promise<boolean> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/income-expense/delete',
      data: { recordId }
    })
  },

  // ==================== 服务评价管理接口 ====================

  // 查询服务评价
  getServiceEvaluation: async (
    orderId: string
  ): Promise<{
    overallRating: number
    professionalRating: number
    attitudeRating: number
    serviceRating: number
    evaluationTags: string
    evaluationContent: string
    evaluationTime: number
    evaluationStatus: string
    evaluationId: number
    customerName: string
    practitionerName: string
    orderId: number
    agencyId: number
    servicePackageId: number
    isRecommend: number
    isAnonymous: number
    likeCount: number
    reviewType: string
    replyUserName?: string
    replyTime?: number
    replyContent?: string
    evaluationImages?: string
  }> => {
    return await request.get({
      url: '/publicbiz/domestic-task-order/evaluation/info',
      params: { orderId }
    })
  },

  // 新增服务评价
  addServiceEvaluation: async (data: {
    orderId: string
    orderNo?: string
    overallRating: number
    evaluationTags?: string
    evaluationContent?: string
    evaluationTime: number
  }): Promise<{ evaluationId: string }> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/evaluation/add',
      data
    })
  },

  // 修改服务评价
  updateServiceEvaluation: async (data: {
    evaluationId?: string
    orderId?: string
    overallRating?: number
    evaluationTags?: string
    evaluationContent?: string
  }): Promise<boolean> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/evaluation/update',
      data
    })
  },

  // 删除服务评价
  deleteServiceEvaluation: async (evaluationId: string): Promise<boolean> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/evaluation/delete',
      data: { evaluationId }
    })
  },

  // ==================== 售后记录管理接口 ====================

  // 查询售后记录
  getAfterSalesList: async (
    orderId: string
  ): Promise<{
    records: Array<{
      id: string
      workOrderType: string
      problemDescription: string
      workOrderStatus: string
      processingResult: string
      createTime: string
    }>
  }> => {
    return await request.get({
      url: '/publicbiz/domestic-task-order/after-sales/list',
      params: { orderId }
    })
  },

  // 新增售后记录
  addAfterSalesRecord: async (data: {
    orderId: string
    workOrderType: string
    problemDescription: string
    workOrderStatus: string
    processingResult?: string
    createTime: string
  }): Promise<{ afterSalesId: string }> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/after-sales/add',
      data
    })
  },

  // 修改售后记录
  updateAfterSalesRecord: async (data: {
    afterSalesId: string
    workOrderType?: string
    problemDescription?: string
    workOrderStatus?: string
    processingResult?: string
  }): Promise<boolean> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/after-sales/update',
      data
    })
  },

  // 删除售后记录
  deleteAfterSalesRecord: async (afterSalesId: string): Promise<boolean> => {
    return await request.post({
      url: '/publicbiz/domestic-task-order/after-sales/delete',
      data: { afterSalesId }
    })
  },

  // ==================== 操作日志管理接口 ====================

  // 获取操作日志列表
  getOperationLogList: async (params: OperationLogParams): Promise<OperationLogPageResult> => {
    return await request.get({
      url: '/publicbiz/domestic-task-order/log/list',
      params
    })
  },

  // ==================== 基础数据查询接口 ====================

  // 获取服务套餐列表
  getServicePackageList: async (params?: {
    category?: string
    status?: string
  }): Promise<{
    packages?: ServicePackage[]
    data?: ServicePackage[]
    total?: number
  }> => {
    return await request.get({
      url: '/publicbiz/domestic-task-order/package/list',
      params
    })
  },

  // 获取服务机构列表
  getServiceAgencyList: async (params?: {
    agencyType?: string
    cooperationStatus?: string
    serviceType?: string
    keyword?: string
  }): Promise<{
    agencies?: ServiceAgency[]
    data?: ServiceAgency[]
    total?: number
  }> => {
    return await request.get({
      url: '/publicbiz/domestic-task-order/agency/list',
      params
    })
  },

  // 获取服务人员列表
  getServicePersonnelList: async (params?: {
    serviceType?: string
    agencyId?: number
    status?: string
    keyword?: string
  }): Promise<{
    practitioners?: ServicePersonnel[]
    data?: ServicePersonnel[]
    total?: number
  }> => {
    return await request.get({
      url: '/publicbiz/domestic-task-order/practitioner/list',
      params
    })
  },

  // 获取客户基本信息
  getCustomerInfo: async (customerOneid: string): Promise<CustomerInfo> => {
    return await request.get({
      url: '/publicbiz/domestic-task-order/customer/info',
      params: { customerOneid }
    })
  },

  // ==================== 人员变动管理接口 ====================

  // 查询人员变动记录
  getPersonnelChangeList: async (
    orderId: string
  ): Promise<{
    changes: PersonnelChangeRecord[]
  }> => {
    return await request.get({
      url: '/publicbiz/domestic-task-order/personnel-change/list',
      params: { orderId }
    })
  },

  // ==================== 统计信息接口 ====================

  // 获取订单统计信息
  getStatistics: async (): Promise<StatisticsInfo> => {
    return await request.get({
      url: '/publicbiz/domestic-task-order/statistics'
    })
  },

  // ==================== 下拉数据接口 ====================

  // 获取订单下拉数据（商机、线索等）
  getDropdownData: async (params: {
    orderType: string
    businessLine?: string
  }): Promise<{
    businessOptions: BusinessOption[]
    leadOptions: LeadOption[]
  }> => {
    return await request.get({
      url: '/publicbiz/order/dropdown-data',
      params
    })
  }
}

// ==================== 数据字典常量 ====================

// 订单状态
export const ORDER_STATUS = {
  PENDING: 'pending',
  ASSIGNED: 'assigned',
  IN_SERVICE: 'in_service',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
} as const

// 支付状态
export const PAYMENT_STATUS = {
  UNPAID: 'unpaid',
  PAID: 'paid',
  PARTIAL: 'partial'
} as const

// 服务类型
export const SERVICE_TYPE = {
  MATERNITY: 'maternity',
  DEEP_CLEANING: 'deep_cleaning',
  HOURLY: 'hourly',
  NANNY: 'nanny',
  RANGE_HOOD_CLEANING: 'range_hood_cleaning',
  DAILY_CLEANING: 'daily_cleaning',
  GLASS_CLEANING: 'glass_cleaning'
} as const

// 任务状态
export const TASK_STATUS = {
  PENDING: 'pending',
  ASSIGNED: 'assigned',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
} as const

// 支付类型
export const PAYMENT_TYPE = {
  CASH: 'cash',
  WECHAT: 'wechat',
  ALIPAY: 'alipay',
  BANK_TRANSFER: 'bank_transfer',
  POS: 'pos',
  OTHER: 'other'
} as const

// 机构类型
export const AGENCY_TYPE = {
  COOPERATION: 'cooperation',
  COMPETITOR: 'competitor',
  OTHER: 'other'
} as const

// 合作状态
export const COOPERATION_STATUS = {
  COOPERATING: 'cooperating',
  SUSPENDED: 'suspended',
  TERMINATED: 'terminated'
} as const

// ==================== 状态映射工具函数 ====================

// 获取订单状态显示文本
export const getOrderStatusText = (status: string): string => {
  if (!status) return '-'
  const statusMap: Record<string, string> = {
    [ORDER_STATUS.PENDING]: '待派单',
    [ORDER_STATUS.ASSIGNED]: '已派单',
    [ORDER_STATUS.IN_SERVICE]: '服务中',
    [ORDER_STATUS.COMPLETED]: '已完成',
    [ORDER_STATUS.CANCELLED]: '已取消'
  }
  return statusMap[status] || '未知'
}

// 获取支付状态显示文本
export const getPaymentStatusText = (status: string): string => {
  if (!status) return '-'
  const statusMap: Record<string, string> = {
    [PAYMENT_STATUS.UNPAID]: '未支付',
    [PAYMENT_STATUS.PAID]: '已支付',
    [PAYMENT_STATUS.PARTIAL]: '部分支付'
  }
  return statusMap[status] || '未知'
}

// 获取服务类型显示文本
export const getServiceTypeText = (type: string): string => {
  if (!type) return '-'
  const typeMap: Record<string, string> = {
    [SERVICE_TYPE.MATERNITY]: '月嫂服务',
    [SERVICE_TYPE.DEEP_CLEANING]: '深度保洁',
    [SERVICE_TYPE.HOURLY]: '小时工',
    [SERVICE_TYPE.NANNY]: '育儿嫂服务',
    [SERVICE_TYPE.RANGE_HOOD_CLEANING]: '油烟机清洗',
    [SERVICE_TYPE.DAILY_CLEANING]: '日常保洁',
    [SERVICE_TYPE.GLASS_CLEANING]: '玻璃清洗'
  }
  return typeMap[type] || '未知'
}

// 获取任务状态显示文本
export const getTaskStatusText = (status: string): string => {
  if (!status) return '-'
  const statusMap: Record<string, string> = {
    [TASK_STATUS.PENDING]: '待指派',
    [TASK_STATUS.ASSIGNED]: '已分配',
    [TASK_STATUS.IN_PROGRESS]: '进行中',
    [TASK_STATUS.COMPLETED]: '已完成',
    [TASK_STATUS.CANCELLED]: '已取消'
  }
  return statusMap[status] || '未知'
}

// 获取支付类型显示文本
export const getPaymentTypeText = (type: string): string => {
  if (!type) return '-'
  const typeMap: Record<string, string> = {
    [PAYMENT_TYPE.CASH]: '现金',
    [PAYMENT_TYPE.WECHAT]: '微信支付',
    [PAYMENT_TYPE.ALIPAY]: '支付宝',
    [PAYMENT_TYPE.BANK_TRANSFER]: '银行转账',
    [PAYMENT_TYPE.POS]: 'POS机刷卡',
    [PAYMENT_TYPE.OTHER]: '其他'
  }
  return typeMap[type] || '未知'
}

// 获取机构类型显示文本
export const getAgencyTypeText = (type: string): string => {
  if (!type) return '-'
  const typeMap: Record<string, string> = {
    [AGENCY_TYPE.COOPERATION]: '合作',
    [AGENCY_TYPE.COMPETITOR]: '竞争对手',
    [AGENCY_TYPE.OTHER]: '其他'
  }
  return typeMap[type] || '未知'
}

// 获取合作状态显示文本
export const getCooperationStatusText = (status: string): string => {
  if (!status) return '-'
  const statusMap: Record<string, string> = {
    [COOPERATION_STATUS.COOPERATING]: '合作中',
    [COOPERATION_STATUS.SUSPENDED]: '已暂停',
    [COOPERATION_STATUS.TERMINATED]: '已终止'
  }
  return statusMap[status] || '未知'
}

// ==================== 状态标签类型映射 ====================

// 获取订单状态标签类型
export const getOrderStatusTagType = (status: string): string => {
  const statusMap: Record<string, string> = {
    [ORDER_STATUS.PENDING]: 'warning',
    [ORDER_STATUS.ASSIGNED]: 'info',
    [ORDER_STATUS.IN_SERVICE]: 'primary',
    [ORDER_STATUS.COMPLETED]: 'success',
    [ORDER_STATUS.CANCELLED]: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取支付状态标签类型
export const getPaymentStatusTagType = (status: string): string => {
  const statusMap: Record<string, string> = {
    [PAYMENT_STATUS.UNPAID]: 'warning',
    [PAYMENT_STATUS.PAID]: 'success',
    [PAYMENT_STATUS.PARTIAL]: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取任务状态标签类型
export const getTaskStatusTagType = (status: string): string => {
  const statusMap: Record<string, string> = {
    [TASK_STATUS.PENDING]: 'info',
    [TASK_STATUS.ASSIGNED]: 'warning',
    [TASK_STATUS.IN_PROGRESS]: 'primary',
    [TASK_STATUS.COMPLETED]: 'success',
    [TASK_STATUS.CANCELLED]: 'danger'
  }
  return statusMap[status] || 'info'
}

// ==================== 默认值常量 ====================

// 默认分页参数
export const DEFAULT_PAGE_PARAMS = {
  page: 1,
  size: 10
} as const

// 默认搜索参数
export const DEFAULT_SEARCH_PARAMS = {
  orderStatus: '',
  paymentStatus: '',
  serviceType: '',
  keyword: '',
  startDate: '',
  endDate: ''
} as const

// 默认订单状态
export const DEFAULT_ORDER_STATUS = ORDER_STATUS.PENDING

// 默认支付状态
export const DEFAULT_PAYMENT_STATUS = PAYMENT_STATUS.UNPAID

// 默认任务状态
export const DEFAULT_TASK_STATUS = TASK_STATUS.PENDING

// ==================== 验证规则常量 ====================

// 订单金额验证规则
export const ORDER_AMOUNT_RULES = {
  required: true,
  min: 0.01,
  max: 999999.99,
  message: '订单金额必须在0.01-999999.99之间'
} as const

// 客户姓名验证规则
export const CUSTOMER_NAME_RULES = {
  required: true,
  min: 2,
  max: 20,
  message: '客户姓名长度必须在2-20个字符之间'
} as const

// 客户电话验证规则
export const CUSTOMER_PHONE_RULES = {
  required: true,
  pattern: /^1[3-9]\d{9}$/,
  message: '请输入正确的手机号码'
} as const

// 服务地址验证规则
export const SERVICE_ADDRESS_RULES = {
  required: true,
  min: 5,
  max: 200,
  message: '服务地址长度必须在5-200个字符之间'
} as const

// ==================== 导出默认接口 ====================

export default HousekeepingServiceOrderApi
