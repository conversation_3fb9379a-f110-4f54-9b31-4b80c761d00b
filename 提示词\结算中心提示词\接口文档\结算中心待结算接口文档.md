# 结算中心待结算接口文档

## 1. 接口概述和基础信息

### 1.1 基础信息
- **基础路径**: `/publicbiz/settlement`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **请求方式**: GET/POST/PUT/DELETE

### 1.2 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-08-16 10:30:00"
}
```

### 1.3 响应状态码说明
- **200**: 请求成功
- **400**: 参数验证错误
- **403**: 权限不足
- **404**: 资源不存在
- **500**: 服务器错误

## 2. 接口详细说明

### 2.1 获取待结算列表

**接口地址**: `GET /publicbiz/settlement/pagePendingSettlement`

**功能说明**: 分页查询待结算订单列表，支持多条件筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNo | Integer | 是 | 当前页码，从1开始 |
| pageSize | Integer | 是 | 每页大小，默认10 |
| startDate | String | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | String | 否 | 结束日期，格式：YYYY-MM-DD |
| agencyName | String | 否 | 机构名称，支持模糊查询 |
| practitionerName | String | 否 | 阿姨姓名，支持模糊查询 |
| orderStatus | String | 否 | 订单状态：completed-已完成/in_service-服务中/cancelled-已取消/refunded-已退款 |
| settlementStatus | String | 否 | 结算状态：pending-待结算/settling-结算中/settled-已结算/failed-结算失败 |


**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| data | Array | 订单列表 |
| data[].id | Long | 订单ID |
| data[].orderNo | String | 订单编号 |
| data[].packageName | String | 套餐名称 |
| data[].agencyId | Long | 机构ID |
| data[].agencyName | String | 机构名称 |
| data[].orderTime | String | 下单时间 |
| data[].completionTime | String | 完成时间 |
| data[].practitionerOneId | String | 阿姨OneID |
| data[].practitionerName | String | 阿姨姓名 |
| data[].orderStatus | String | 订单状态 |
| data[].settlementStatus | String | 结算状态 |
| data[].orderAmount | BigDecimal | 订单金额 |
| total | Integer | 总记录数 |
| pageNo | Integer | 当前页码 |
| pageSize | Integer | 每页大小 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "orderNo": "DD20240815001",
      "packageName": "金牌月嫂26天服务",
      "agencyId": 1001,
      "agencyName": "阳光家政",
      "orderTime": "2024-08-01 10:30:00",
      "completionTime": "2024-08-15 18:00:00",
      "practitionerOneId": "PR001",
      "practitionerName": "王阿姨",
      "orderStatus": "completed",
      "settlementStatus": "pending",
      "orderAmount": 8800.00
    }
  ],
  "total": 15,
  "pageNo": 1,
  "pageSize": 10,
  "timestamp": "2024-08-16 10:30:00"
}
```

### 2.2 获取订单数量统计

**接口地址**: `GET /publicbiz/settlement/getPendingCount`

**功能说明**: 获取订单订单数量统计

**请求参数**: 无

**请求示例**:
```bash
GET /publicbiz/settlement/getPendingCount
```

**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| data.pendingCount | Integer | 待结算数量 |
| data.reconciliationCount | Integer | 对账单数量 |
| data.invoiceCount | Integer | 发票管理数量 |
| data.fundsCount | Integer | 资金列表数量 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "pendingCount": 15,
    "reconciliationCount": 25,
    "invoiceCount": 18,
    "fundsCount": 42
  },
  "timestamp": "2024-08-16 10:30:00"
}
```

### 2.3 生成对账单

**接口地址**: `POST /publicbiz/settlement/generateReconciliation`

**功能说明**: 根据选中的订单生成对账单

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orders | Array | 是 | 订单列表 |
| orders[].id | Long | 是 | 订单ID |
| orders[].orderNo | String | 是 | 订单编码 |
| orders[].packageName | String | 是 | 套餐名称 |
| orders[].agencyId | Long | 是 | 机构ID |
| orders[].agencyName | String | 是 | 家政机构 |
| orders[].orderTime | String | 是 | 下单时间，格式：YYYY-MM-DD HH:mm:ss |
| orders[].completionTime | String | 否 | 完成时间，格式：YYYY-MM-DD HH:mm:ss |
| orders[].practitionerOneId | String | 是 | 阿姨OneID |
| orders[].practitionerName | String | 是 | 阿姨姓名 |
| orders[].orderStatus | String | 是 | 订单状态：completed-已完成/in_service-服务中/cancelled-已取消/refunded-已退款 |
| orders[].settlementStatus | String | 是 | 结算状态：pending-待结算/settling-结算中/settled-已结算/failed-结算失败 |
| totalAmount | BigDecimal | 是 | 对账总金额 |
| agencyAmount | BigDecimal | 是 | 机构分成金额 |
| platformAmount | BigDecimal | 是 | 平台分成金额 |
| agencyRatio | BigDecimal | 是 | 机构分成比例(%) |
| platformRatio | BigDecimal | 是 | 平台分成比例(%) |

**请求示例**:
```json
{
  "orders": [
    {
      "id": 1,
      "orderNo": "DD20240815001",
      "packageName": "金牌月嫂26天服务",
      "agencyId": 1001,
      "agencyName": "阳光家政",
      "orderTime": "2024-08-01 10:30:00",
      "completionTime": "2024-08-15 18:00:00",
      "practitionerOneId": "PR001",
      "practitionerName": "王阿姨",
      "orderStatus": "completed",
      "settlementStatus": "pending"
    },
    {
      "id": 2,
      "orderNo": "DD20240815002",
      "packageName": "高级育婴师服务",
      "agencyId": 1002,
      "agencyName": "爱心家政",
      "orderTime": "2024-08-02 14:20:00",
      "completionTime": "2024-08-16 17:30:00",
      "practitionerOneId": "PR002",
      "practitionerName": "李阿姨",
      "orderStatus": "completed",
      "settlementStatus": "pending"
    }
  ],
  "totalAmount": 9360.00,
  "agencyAmount": 7488.00,
  "platformAmount": 1872.00,
  "agencyRatio": 80.00,
  "platformRatio": 20.00
}
```

**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| data.statementNo | String | 对账单号 |
| data.orders | Array | 包含的订单列表 |
| data.orders[].id | Long | 订单ID |
| data.orders[].orderNo | String | 订单编码 |
| data.orders[].packageName | String | 套餐名称 |
| data.orders[].agencyId | Long | 机构ID |
| data.orders[].agencyName | String | 家政机构 |
| data.orders[].orderTime | String | 下单时间 |
| data.orders[].completionTime | String | 完成时间 |
| data.orders[].practitionerOneId | String | 阿姨OneID |
| data.orders[].practitionerName | String | 阿姨姓名 |
| data.orders[].orderStatus | String | 订单状态 |
| data.orders[].settlementStatus | String | 结算状态 |
| data.totalAmount | BigDecimal | 对账总金额 |
| data.agencyAmount | BigDecimal | 机构分成金额 |
| data.platformAmount | BigDecimal | 平台分成金额 |
| data.agencyRatio | BigDecimal | 机构分成比例(%) |
| data.platformRatio | BigDecimal | 平台分成比例(%) |
| data.orderCount | Integer | 包含订单数量 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "statementNo": "ZD20240816001",
    "orders": [
      {
        "id": 1,
        "orderNo": "DD20240815001",
        "packageName": "金牌月嫂26天服务",
        "agencyName": "阳光家政",
        "orderTime": "2024-08-01 10:30:00",
        "completionTime": "2024-08-15 18:00:00",
        "practitionerName": "王阿姨",
        "orderStatus": "completed",
        "settlementStatus": "pending"
      },
      {
        "id": 2,
        "orderNo": "DD20240815002",
        "packageName": "高级育婴师服务",
        "agencyName": "爱心家政",
        "orderTime": "2024-08-02 14:20:00",
        "completionTime": "2024-08-16 17:30:00",
        "practitionerName": "李阿姨",
        "orderStatus": "completed",
        "settlementStatus": "pending"
      }
    ],
    "totalAmount": 9360.00,
    "agencyAmount": 7488.00,
    "platformAmount": 1872.00,
    "agencyRatio": 80.00,
    "platformRatio": 20.00,
    "orderCount": 2
  },
  "timestamp": "2024-08-16 10:30:00"
}
```

### 2.4 获取订单详情

**接口地址**: `GET /publicbiz/settlement/getOrderDetail/{orderNo}`

**功能说明**: 根据订单号获取订单详细信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderNo | String | 是 | 订单编号（路径参数） |

**请求示例**:
```bash
GET /publicbiz/settlement/getOrderDetail/DD20240815001
```

**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| data.basicInfo | Object | 基础信息 |
| data.basicInfo.orderNo | String | 订单编号 |
| data.basicInfo.orderStatus | String | 订单状态 |
| data.basicInfo.createTime | String | 创建时间 |
| data.basicInfo.paymentTime | String | 支付时间 |
| data.basicInfo.completionTime | String | 完成时间 |
| data.serviceInfo | Object | 服务信息 |
| data.serviceInfo.packageName | String | 套餐名称 |
| data.serviceInfo.serviceType | String | 服务类型 |
| data.serviceInfo.serviceDuration | String | 服务时长 |
| data.serviceInfo.serviceAddress | String | 服务地址 |
| data.agencyInfo | Object | 机构信息 |
| data.agencyInfo.agencyName | String | 机构名称 |
| data.agencyInfo.agencyPhone | String | 机构电话 |
| data.agencyInfo.practitionerName | String | 阿姨姓名 |
| data.agencyInfo.practitionerPhone | String | 阿姨电话 |
| data.customerInfo | Object | 客户信息 |
| data.customerInfo.customerName | String | 客户姓名 |
| data.customerInfo.customerPhone | String | 客户电话 |
| data.customerInfo.customerAddress | String | 客户地址 |
| data.customerInfo.specialRequirements | String | 特殊要求 |
| data.costInfo | Object | 费用信息 |
| data.costInfo.orderAmount | BigDecimal | 订单金额 |
| data.costInfo.agencyCommission | BigDecimal | 机构分成 |
| data.costInfo.platformCommission | BigDecimal | 平台分成 |
| data.costInfo.paymentMethod | String | 支付方式 |
| data.serviceRecords | Array | 服务记录 |
| data.serviceRecords[].id | Long | 记录ID |
| data.serviceRecords[].time | String | 记录时间 |
| data.serviceRecords[].title | String | 记录标题 |
| data.serviceRecords[].description | String | 记录描述 |
| data.serviceRecords[].status | String | 记录状态 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "basicInfo": {
      "orderNo": "DD20240815001",
      "orderStatus": "completed",
      "createTime": "2024-08-01 10:30:00",
      "paymentTime": "2024-08-01 10:35:00",
      "completionTime": "2024-08-15 18:00:00"
    },
    "serviceInfo": {
      "packageName": "金牌月嫂26天服务",
      "serviceType": "月嫂服务",
      "serviceDuration": "26天",
      "serviceAddress": "北京市朝阳区建国路88号金地中心A座1201"
    },
    "agencyInfo": {
      "agencyName": "阳光家政",
      "agencyPhone": "010-12345678",
      "practitionerName": "王阿姨",
      "practitionerPhone": "138-0000-1234"
    },
    "customerInfo": {
      "customerName": "李女士",
      "customerPhone": "139-0000-5678",
      "customerAddress": "北京市朝阳区建国路88号金地中心A座1201",
      "specialRequirements": "需要有育儿经验,会做月子餐"
    },
    "costInfo": {
      "orderAmount": 8800.00,
      "agencyCommission": 7040.00,
      "platformCommission": 1760.00,
      "paymentMethod": "微信支付"
    },
    "serviceRecords": [
      {
        "id": 1,
        "time": "2024-08-01 10:30:00",
        "title": "订单创建",
        "description": "客户下单成功,等待机构确认",
        "status": "已完成"
      }
    ]
  },
  "timestamp": "2024-08-16 10:30:00"
}
```

### 2.5 导出订单详情

**接口地址**: `GET /publicbiz/settlement/exportOrderDetail/{orderNo}`

**功能说明**: 导出订单详情为Excel文件

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderNo | String | 是 | 订单编号（路径参数） |

**请求示例**:
```bash
GET /publicbiz/settlement/exportOrderDetail/DD20240815001
```

**响应**: 返回Excel文件流

### 2.6 分页查询订单资金列表

**接口地址**: `GET /publicbiz/settlement/orderFunds/page`

**功能说明**: 分页查询订单资金列表，支持多条件筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderNo | String | 否 | 订单编号 |
| agencyName | String | 否 | 机构名称 |
| practitionerName | String | 否 | 服务阿姨姓名 |
| statementNo | String | 否 | 对账单号 |
| orderStatus | String | 否 | 订单状态 |
| fundStatus | String | 否 | 资金状态 |
| invoiceStatus | String | 否 | 开票状态 |
| startDate | String | 否 | 开始日期(YYYY-MM-DD) |
| endDate | String | 否 | 结束日期(YYYY-MM-DD) |
| pageNo | Integer | 是 | 页码，从1开始 |
| pageSize | Integer | 是 | 每页大小，默认10 |

**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码 |
| message | String | 响应消息 |
| data | Array | 订单资金列表 |
| total | Integer | 总记录数 |
| pageNo | Integer | 当前页码 |
| pageSize | Integer | 每页大小 |

| data[].id | Long | 主键ID |
| data[].orderNo | String | 订单编号 |
| data[].packageName | String | 套餐名称 |
| data[].agencyName | String | 家政机构 |
| data[].practitionerName | String | 服务阿姨 |
| data[].orderAmount | BigDecimal | 订单金额 |
| data[].platformCommission | BigDecimal | 平台分成 |
| data[].agencyCommission | BigDecimal | 机构分成 |
| data[].orderStatus | String | 订单状态 |
| data[].fundStatus | String | 资金状态 |
| data[].statementNo | String | 对账单号 |
| data[].invoiceStatus | String | 开票状态 |
| data[].invoiceNo | String | 发票号 |
| data[].createTime | String | 创建时间 |
| data[].paymentTime | String | 支付时间 |


**请求示例**:
```bash
GET /publicbiz/settlement/orderFunds/page?orderNo=DD20240815001&agencyName=阳光家政&pageNo=1&pageSize=10
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "orderNo": "DD20240815001",
      "packageName": "金牌月嫂26天服务",
      "agencyName": "阳光家政",
      "practitionerName": "王阿姨",
      "orderAmount": 8800.00,
      "platformCommission": 880.00,
      "agencyCommission": 7920.00,
      "orderStatus": "completed",
      "fundStatus": "settled",
      "statementNo": "ZD20240816001",
      "invoiceStatus": "invoiced",
      "invoiceNo": "FP20240816001",
      "createTime": "2024-08-01 10:30:00",
      "paymentTime": "2024-08-01 10:35:00"
    }
  ],
  "total": 15,
  "pageNo": 1,
  "pageSize": 10,
  "timestamp": "2024-08-16 10:30:00"
}
```

### 2.7 导出订单资金列表

**接口地址**: `GET /publicbiz/settlement/orderFunds/export`

**功能说明**: 导出订单资金列表为Excel文件

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderNo | String | 否 | 订单编号 |
| agencyName | String | 否 | 机构名称 |
| practitionerName | String | 否 | 服务阿姨姓名 |
| statementNo | String | 否 | 对账单号 |
| orderStatus | String | 否 | 订单状态 |
| fundStatus | String | 否 | 资金状态 |
| invoiceStatus | String | 否 | 开票状态 |
| startDate | String | 否 | 开始日期(YYYY-MM-DD) |
| endDate | String | 否 | 结束日期(YYYY-MM-DD) |

**请求示例**:
```bash
GET /publicbiz/settlement/orderFunds/export?startDate=2024-08-01&endDate=2024-08-16
```

**响应**: 返回Excel文件流

## 3. 数据字典

### 3.1 订单状态枚举

| 枚举值 | 说明 |
|--------|------|
| completed | 已完成 |
| in_service | 服务中 |
| cancelled | 已取消 |
| refunded | 已退款 |

### 3.2 结算状态枚举

| 枚举值 | 说明 |
|--------|------|
| pending | 待结算 |
| settling | 结算中 |
| settled | 已结算 |
| failed | 结算失败 |

### 3.3 结算类型枚举

| 枚举值 | 说明 |
|--------|------|
| order | 订单结算 |
| practitioner | 阿姨结算 |
| agency | 机构结算 |
| platform | 平台结算 |

### 3.4 结算周期枚举

| 枚举值 | 说明 |
|--------|------|
| daily | 日结 |
| weekly | 周结 |
| monthly | 月结 |

### 3.5 结算方式枚举

| 枚举值 | 说明 |
|--------|------|
| bank_transfer | 银行转账 |
| alipay | 支付宝 |
| wechat | 微信 |
| other | 其他 |

### 3.6 对账单类型枚举

| 枚举值 | 说明 |
|--------|------|
| agency | 机构对账 |
| platform | 平台对账 |
| practitioner | 阿姨对账 |

### 3.7 对账状态枚举

| 枚举值 | 说明 |
|--------|------|
| pending | 待对账确认 |
| confirmed | 已确认 |
| paid | 已支付 |
| cancelled | 已取消 |


### 3.9 发票类型枚举

| 枚举值 | 说明 |
|--------|------|
| 增值税普通发票 | 增值税普通发票 |
| 增值税专用发票 | 增值税专用发票 |
| 电子发票 | 电子发票 |

### 3.10 开票状态枚举

| 枚举值 | 说明 |
|--------|------|
| not_invoiced | 未开票 |
| invoicing | 开票中 |
| invoiced | 已开票 |
| failed | 开票失败 |

## 4. 错误码说明

### 4.1 通用错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 参数验证错误 | 检查请求参数格式和必填项 |
| 403 | 权限不足 | 检查用户权限和登录状态 |
| 404 | 资源不存在 | 检查请求的资源ID或路径是否正确 |
| 500 | 服务器错误 | 联系技术支持 |

### 4.2 业务错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 1001 | 订单不存在 | 检查订单编号是否正确 |
| 1002 | 订单状态不允许操作 | 检查订单当前状态 |
| 1003 | 分成比例总和不为100% | 调整机构分成比例和平台分成比例 |
| 1004 | 对账单生成失败 | 检查订单数据完整性 |
| 1005 | 对账状态更新失败 | 检查订单当前对账状态 |
| 1006 | 订单已被选中生成对账单 | 检查订单是否已被选中 |

## 5. 注意事项

1. **分页查询**: 所有分页接口的page参数从1开始，size参数建议不超过100
2. **日期格式**: 所有日期参数使用YYYY-MM-DD格式，时间参数使用YYYY-MM-DD HH:mm:ss格式
3. **金额精度**: 所有金额字段保留2位小数
4. **权限控制**: 部分接口需要特定权限，请确保用户具有相应权限
5. **数据一致性**: 批量操作时，建议先查询数据状态，再进行操作
6. **错误处理**: 建议对所有接口调用进行异常处理，特别是网络异常情况 