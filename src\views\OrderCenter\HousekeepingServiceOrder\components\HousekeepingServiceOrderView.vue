<!--
  页面名称：家政服务订单查看详情
  功能描述：展示家政服务订单详情，支持多个标签页切换
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    title="家政服务订单详情"
    direction="rtl"
    size="80%"
    :before-close="handleClose"
  >
    <div class="order-view-container">
      <!-- 订单头部信息 -->
      <div class="order-header">
        <div class="service-banner">
          <div class="service-title"
            >{{ orderDetail.serviceType || '家政服务' }} -
            {{ orderDetail.customerName || '客户' }}</div
          >
          <div class="service-status">
            <el-button size="small" type="primary" plain>{{
              getServiceStatusText(
                (orderDetail as any).serviceStatus || (orderDetail as any).orderStatus || 'pending'
              )
            }}</el-button>
          </div>
          <!-- 隐藏编辑按钮 -->
          <!-- <el-button size="small" type="primary" plain class="edit-btn" @click="handleEdit">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button> -->
        </div>
        <div class="order-info">
          <div class="order-number"
            >订单号：{{ orderDetail.orderNumber || (orderDetail as any).orderNo || '-' }}</div
          >
          <div class="payment-status">
            支付状态：
            <el-tag type="success" size="small">{{
              getPaymentStatusText(orderDetail.paymentStatus || 'paid')
            }}</el-tag>
          </div>
        </div>

        <!-- 调试信息 -->
        <div
          class="debug-info"
          style="
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
            font-size: 12px;
          "
        >
          <div>调试信息：</div>
          <div>订单ID: {{ orderDetail.id }}</div>
          <div>客户姓名: {{ orderDetail.customerName }}</div>
          <div>客户电话: {{ orderDetail.customerPhone }}</div>
          <div>服务地址: {{ orderDetail.serviceAddress }}</div>
          <div>服务类型: {{ orderDetail.serviceType }}</div>
          <div>订单金额: {{ orderDetail.totalAmount }}</div>
          <div>服务人员: {{ orderDetail.practitionerName }}</div>
          <div>服务机构: {{ orderDetail.agencyName }}</div>
        </div>
      </div>

      <!-- 标签页导航 -->
      <div class="tab-navigation">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="基本信息" name="basic" />
          <el-tab-pane label="服务任务列表" name="tasks" />
          <el-tab-pane label="收款信息" name="payment" />
          <el-tab-pane label="操作日志" name="operation" />
          <el-tab-pane label="人员变动" name="personnel" />
          <el-tab-pane label="收支记录" name="income" />
          <el-tab-pane label="服务评价" name="evaluation" />
          <el-tab-pane label="售后记录" name="afterSales" />
        </el-tabs>
      </div>

      <!-- 内容区域 -->
      <div class="content-area">
        <div v-if="activeTab === 'basic'">
          <BasicInfoTab :order-detail="orderDetail" />
        </div>
        <div v-else-if="activeTab === 'tasks'">
          <ServiceTaskTab :order-detail="orderDetail" />
        </div>
        <div v-else-if="activeTab === 'payment'">
          <PaymentInfoTab :order-detail="orderDetail" />
        </div>
        <div v-else-if="activeTab === 'operation'">
          <OperationLogTab :order-detail="orderDetail" @view-full-log="handleViewFullLog" />
        </div>
        <div v-else-if="activeTab === 'personnel'">
          <PersonnelChangeTab :order-detail="orderDetail" />
        </div>
        <div v-else-if="activeTab === 'income'">
          <IncomeExpenseTab
            ref="incomeExpenseTabRef"
            :order-detail="orderDetail"
            :records="incomeExpenseRecords"
            @add-record="handleAddRecord"
            @edit-record="handleEditRecord"
            @update-records="handleUpdateIncomeRecords"
          />
        </div>
        <div v-else-if="activeTab === 'evaluation'">
          <ServiceEvaluationTab
            ref="serviceEvaluationTabRef"
            :order-detail="orderDetail"
            @add-evaluation="handleAddEvaluation"
            @edit-evaluation="handleEditEvaluation"
          />
        </div>
        <div v-else-if="activeTab === 'afterSales'">
          <AfterSalesTab
            ref="afterSalesTabRef"
            :order-detail="orderDetail"
            :records="afterSalesRecords"
            @add-after-sales="handleAddAfterSales"
            @edit-record="handleEditAfterSales"
            @update-records="handleUpdateAfterSalesRecords"
          />
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </div>

    <!-- 新增收支记录抽屉 -->
    <IncomeExpenseForm
      v-model:visible="incomeFormVisible"
      :order-id="orderDetail.id"
      :record-data="editingRecord"
      @success="handleIncomeSuccess"
    />

    <!-- 新增售后工单抽屉 -->
    <AfterSalesForm
      v-model:visible="afterSalesFormVisible"
      :order-id="orderDetail.id"
      :record-data="editingAfterSalesRecord"
      @success="handleAfterSalesSuccess"
    />

    <!-- 服务评价表单抽屉 -->
    <ServiceEvaluationForm
      v-model:visible="evaluationFormVisible"
      :order-id="orderDetail.id"
      :order-no="orderDetail.orderNumber"
      :evaluation-data="editingEvaluationRecord"
      @success="handleEvaluationSuccess"
    />

    <!-- 调试信息 -->
    <div
      v-if="evaluationFormVisible"
      style="
        position: fixed;
        top: 10px;
        right: 10px;
        background: #f0f0f0;
        padding: 10px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 9999;
      "
    >
      <div>调试信息：</div>
      <div>orderDetail.id: {{ orderDetail.id }}</div>
      <div>orderDetail.orderNumber: {{ orderDetail.orderNumber }}</div>
      <div>editingEvaluationRecord: {{ editingEvaluationRecord }}</div>
    </div>

    <!-- 操作日志抽屉 -->
    <OptLog v-model:visible="optLogVisible" :order-data="orderDetail" />
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit } from '@element-plus/icons-vue'
import BasicInfoTab from './tabs/BasicInfoTab.vue'
import ServiceTaskTab from './tabs/ServiceTaskTab.vue'
import PaymentInfoTab from './tabs/PaymentInfoTab.vue'
import OperationLogTab from './tabs/OperationLogTab.vue'
import PersonnelChangeTab from './tabs/PersonnelChangeTab.vue'
import IncomeExpenseTab from './tabs/IncomeExpenseTab.vue'
import ServiceEvaluationTab from './tabs/ServiceEvaluationTab.vue'
import AfterSalesTab from './tabs/AfterSalesTab.vue'
import { HousekeepingServiceOrderApi } from '@/api/OrderCenter/HousekeepingServiceOrder'
import IncomeExpenseForm from './forms/IncomeExpenseForm.vue'
import AfterSalesForm from './forms/AfterSalesForm.vue'
import ServiceEvaluationForm from './forms/ServiceEvaluationForm.vue'
import OptLog from './OptLog.vue'

// Props
interface Props {
  visible: boolean
  orderId?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderId: ''
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  edit: [orderData: any]
}>()

// 响应式数据
const activeTab = ref('basic')
const incomeFormVisible = ref(false)
const afterSalesFormVisible = ref(false)
const evaluationFormVisible = ref(false)
const optLogVisible = ref(false)
const editingRecord = ref<any>(null)
const editingAfterSalesRecord = ref<any>(null)
const editingEvaluationRecord = ref<any>(null)
const incomeExpenseTabRef = ref()
const afterSalesTabRef = ref()
const serviceEvaluationTabRef = ref()

// 收支记录数据
const incomeExpenseRecords = ref([
  {
    id: 1,
    type: '额外收入',
    amount: 50,
    date: '2024-06-10',
    description: '额外帮助客户采购婴儿用品,客户支付的跑腿费。'
  },
  {
    id: 2,
    type: '服务收入',
    amount: 12800,
    date: '2024-06-01',
    description: '月嫂服务基础费用。'
  },
  {
    id: 3,
    type: '额外支出',
    amount: 200,
    date: '2024-06-05',
    description: '购买婴儿用品垫付费用。'
  }
])

// 售后记录数据
const afterSalesRecords = ref([
  {
    id: 1,
    workOrderType: '物品损坏',
    problemDescription: '客户反映在服务过程中，婴儿床的床垫出现了轻微磨损。',
    workOrderStatus: 'pending',
    processingResult: '',
    createTime: '2024-06-15 10:30:00'
  },
  {
    id: 2,
    workOrderType: '服务不满意',
    problemDescription: '客户对服务人员的专业度表示不满意，希望更换服务人员。',
    workOrderStatus: 'processing',
    processingResult: '已安排新的服务人员，正在协调交接事宜。',
    createTime: '2024-06-12 14:20:00'
  }
])

// 订单详情数据
const orderDetail = reactive({
  id: '',
  orderNumber: '',
  customerName: '',
  customerPhone: '',
  serviceType: '',
  serviceAgency: '',
  servicePersonnel: '',
  serviceAddress: '',
  serviceAmount: 0,
  paymentStatus: '',
  serviceStatus: '',
  appointmentTime: '',
  // 新增字段
  businessOpportunity: '',
  lead: '',
  servicePackage: '',
  serviceStartDate: '',
  serviceEndDate: '',
  serviceDuration: '',
  serviceFrequency: '',
  serviceDescription: '',
  unitPrice: 0,
  totalAmount: 0,
  discountAmount: 0,
  actualAmount: 0,
  practitionerOneid: '',
  agencyId: 0,
  remark: '',
  createTime: '',
  updateTime: '',
  // 支付信息字段
  paymentMethod: '',
  receivedAmount: 0,
  paymentTime: '',
  paymentRemark: '',
  // 服务机构信息字段
  agencyName: '',
  agencyCode: '',
  agencyType: '',
  cooperationStatus: '',
  contactPerson: '',
  contactPhone: '',
  // 服务人员信息字段
  practitionerName: '',
  practitionerPhone: '',
  experienceYears: 0,
  rating: 0,
  // 任务信息字段
  totalTasks: 0,
  completedTasks: 0,
  taskProgress: 0
})

// 方法定义
const handleClose = () => {
  emit('update:visible', false)
}

// 获取订单详情
const fetchOrderDetail = async (orderId: string) => {
  if (!orderId) return

  try {
    console.log('开始获取订单详情，订单ID:', orderId)
    const result = await HousekeepingServiceOrderApi.getOrderDetail(orderId)
    console.log('订单详情API响应:', result)

    // 时间戳转换函数
    const formatTimestamp = (timestamp: number | null | undefined): string => {
      if (!timestamp) return '-'
      try {
        const date = new Date(timestamp)
        return date.toLocaleString('zh-CN')
      } catch (error) {
        return '-'
      }
    }

    // 格式化预约时间
    const formatAppointmentTime = (timestamp: number): string => {
      if (!timestamp) return '-'
      try {
        const date = new Date(timestamp)
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        })
      } catch (error) {
        return '-'
      }
    }

    // 详细调试日志
    console.log('=== 详细数据结构分析 ===')
    console.log('result:', result)
    console.log('result.practitionerInfo:', result.practitionerInfo)
    console.log('result.agencyInfo:', result.agencyInfo)
    console.log('result.orderInfo:', result.orderInfo)
    console.log('result.customerInfo:', result.customerInfo)
    console.log('result.serviceInfo:', result.serviceInfo)
    console.log('result.paymentInfo:', result.paymentInfo)
    console.log('result.taskInfo:', result.taskInfo)

    // 检查orderInfo中的所有字段
    console.log('=== orderInfo详细字段分析 ===')
    console.log('result.orderInfo.id:', result.orderInfo?.id)
    console.log('result.orderInfo.orderNumber:', result.orderInfo?.orderNumber)
    console.log('result.orderInfo.orderStatus:', result.orderInfo?.orderStatus)
    console.log('result.orderInfo.paymentStatus:', result.orderInfo?.paymentStatus)
    console.log('result.orderInfo.createTime:', result.orderInfo?.createTime)
    console.log('result.orderInfo.updateTime:', result.orderInfo?.updateTime)
    console.log('result.orderInfo.opportunityId:', result.orderInfo?.opportunityId)
    console.log('result.orderInfo.leadId:', result.orderInfo?.leadId)
    console.log('result.orderInfo.totalAmount:', result.orderInfo?.totalAmount)
    console.log('result.orderInfo.servicePackage:', result.orderInfo?.servicePackage)
    console.log('result.orderInfo.serviceAddress:', result.orderInfo?.serviceAddress)
    console.log('result.orderInfo.practitionerOneid:', result.orderInfo?.practitionerOneid)

    // 检查所有可能的字段
    console.log('=== 所有可能的字段 ===')
    console.log('Object.keys(result.orderInfo):', Object.keys(result.orderInfo || {}))
    console.log('Object.keys(result):', Object.keys(result))

    // 灵活的数据处理逻辑，支持多种数据结构
    const resultAny = result as any
    let processedData = {
      // 订单基本信息 - 支持多种字段名
      id: resultAny.orderInfo?.id?.toString() || resultAny.id?.toString() || '',
      orderNumber:
        resultAny.orderInfo?.orderNumber || resultAny.orderNumber || resultAny.orderNo || '',
      orderStatus: resultAny.orderInfo?.orderStatus || resultAny.orderStatus || '',
      paymentStatus: resultAny.orderInfo?.paymentStatus || resultAny.paymentStatus || '',
      createTime: formatTimestamp(resultAny.orderInfo?.createTime || resultAny.createTime),
      updateTime: formatTimestamp(resultAny.orderInfo?.updateTime || resultAny.updateTime),

      // 商机和线索信息
      businessOpportunity: resultAny.orderInfo?.opportunityId || resultAny.opportunityId || '',
      lead: resultAny.orderInfo?.leadId || resultAny.leadId || '',

      // 客户信息 - 支持多种字段名
      customerName: resultAny.customerInfo?.customerName || resultAny.customerName || '',
      customerPhone: resultAny.customerInfo?.customerPhone || resultAny.customerPhone || '',
      serviceAddress:
        resultAny.customerInfo?.serviceAddress ||
        resultAny.serviceAddress ||
        resultAny.orderInfo?.serviceAddress ||
        '',
      remark: resultAny.customerInfo?.customerRemark || resultAny.remark || '',

      // 服务信息 - 支持多种字段名
      serviceType: resultAny.serviceInfo?.serviceType || resultAny.serviceType || '',
      servicePackage:
        resultAny.serviceInfo?.servicePackage ||
        resultAny.servicePackage ||
        resultAny.orderInfo?.servicePackage ||
        '',
      serviceStartDate: resultAny.serviceInfo?.serviceStartDate || resultAny.serviceStartDate || '',
      serviceEndDate: resultAny.serviceInfo?.serviceEndDate || resultAny.serviceEndDate || '',
      serviceDuration: resultAny.serviceInfo?.serviceDuration || resultAny.serviceDuration || '',
      serviceFrequency: resultAny.serviceInfo?.serviceFrequency || resultAny.serviceFrequency || '',
      serviceDescription:
        resultAny.serviceInfo?.serviceDescription || resultAny.serviceDescription || '',
      appointmentTime: formatAppointmentTime(
        resultAny.serviceInfo?.serviceStartDate ||
          resultAny.serviceStartDate ||
          resultAny.appointmentTime ||
          0
      ),
      unitPrice: resultAny.unitPrice || 0,
      totalAmount:
        resultAny.serviceInfo?.serviceAmount ||
        resultAny.totalAmount ||
        resultAny.orderInfo?.totalAmount ||
        0,
      actualAmount: resultAny.paymentInfo?.actualAmount || resultAny.actualAmount || 0,
      discountAmount: resultAny.paymentInfo?.discountAmount || resultAny.discountAmount || 0,

      // 阿姨信息 - 支持多种字段名
      practitionerOneid:
        resultAny.orderInfo?.practitionerOneid || resultAny.practitionerOneid || '',
      practitionerName:
        resultAny.practitionerInfo?.practitionerName ||
        resultAny.practitionerName ||
        resultAny.servicePersonnel ||
        '',
      practitionerPhone:
        resultAny.practitionerInfo?.practitionerPhone || resultAny.practitionerPhone || '',
      experienceYears:
        resultAny.practitionerInfo?.experienceYears || resultAny.experienceYears || 0,
      rating: resultAny.practitionerInfo?.rating || resultAny.rating || 0,

      // 机构信息 - 支持多种字段名
      agencyId: resultAny.agencyInfo?.agencyId || resultAny.agencyId || 0,
      agencyName:
        resultAny.agencyInfo?.agencyName || resultAny.agencyName || resultAny.serviceAgency || '',
      agencyCode: resultAny.agencyInfo?.agencyCode || resultAny.agencyCode || '',
      agencyType: resultAny.agencyInfo?.agencyType || resultAny.agencyType || '',
      cooperationStatus:
        resultAny.agencyInfo?.cooperationStatus || resultAny.cooperationStatus || '',
      contactPerson: resultAny.agencyInfo?.contactPerson || resultAny.contactPerson || '',
      contactPhone: resultAny.agencyInfo?.contactPhone || resultAny.contactPhone || '',

      // 支付信息 - 支持多种字段名
      paymentMethod: resultAny.paymentInfo?.paymentMethod || resultAny.paymentMethod || '',
      receivedAmount: resultAny.paymentInfo?.receivedAmount || resultAny.receivedAmount || 0,
      paymentTime: formatTimestamp(resultAny.paymentInfo?.paymentTime || resultAny.paymentTime),
      paymentRemark: resultAny.paymentInfo?.paymentRemark || resultAny.paymentRemark || '',

      // 任务信息 - 支持多种字段名
      totalTasks: resultAny.taskInfo?.totalTasks || resultAny.totalTasks || 0,
      completedTasks: resultAny.taskInfo?.completedTasks || resultAny.completedTasks || 0,
      taskProgress: resultAny.taskInfo?.taskProgress || resultAny.taskProgress || 0,

      // 兼容字段
      serviceAmount:
        resultAny.serviceInfo?.serviceAmount ||
        resultAny.totalAmount ||
        resultAny.orderInfo?.totalAmount ||
        0,
      serviceStatus: resultAny.orderInfo?.orderStatus || resultAny.orderStatus || ''
    }

    // 更新订单详情数据
    Object.assign(orderDetail, processedData)

    console.log('更新后的订单详情:', orderDetail)
    console.log('=== 字段映射结果 ===')
    console.log('practitionerName:', orderDetail.practitionerName)
    console.log('practitionerPhone:', orderDetail.practitionerPhone)
    console.log('agencyName:', orderDetail.agencyName)
    console.log('agencyCode:', orderDetail.agencyCode)
    console.log('customerName:', orderDetail.customerName)
    console.log('customerPhone:', orderDetail.customerPhone)
    console.log('serviceAddress:', orderDetail.serviceAddress)
    console.log('totalAmount:', orderDetail.totalAmount)
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  }
}

const handleEdit = () => {
  emit('edit', orderDetail)
}

const handleViewFullLog = () => {
  optLogVisible.value = true
}

const handleTabClick = (tab: any) => {
  console.log('切换到标签页:', tab.props.name)
}

const handleAddRecord = () => {
  editingRecord.value = null
  incomeFormVisible.value = true
}

const handleEditRecord = (record: any) => {
  // 设置编辑数据并打开表单
  editingRecord.value = record
  incomeFormVisible.value = true
}

const handleAddAfterSales = () => {
  afterSalesFormVisible.value = true
}

const handleIncomeSuccess = (data?: any) => {
  const wasEditing = editingRecord.value !== null
  console.log('收支记录成功处理:', { data, wasEditing, editingRecord: editingRecord.value })

  if (data) {
    if (wasEditing && editingRecord.value) {
      // 编辑模式：更新现有记录
      const currentRecord = editingRecord.value
      const updatedRecord = { ...currentRecord, ...data }
      console.log('调用更新方法:', updatedRecord)
      incomeExpenseTabRef.value?.updateRecord(updatedRecord)
    } else {
      // 新增模式：添加新记录
      console.log('调用添加方法:', data)
      incomeExpenseTabRef.value?.addRecord(data)
    }
  }

  incomeFormVisible.value = false
  editingRecord.value = null
}

const handleUpdateIncomeRecords = (records: any[]) => {
  console.log('更新收支记录数据:', records)
  incomeExpenseRecords.value = records
}

const handleUpdateAfterSalesRecords = (records: any[]) => {
  console.log('更新售后记录数据:', records)
  afterSalesRecords.value = records
}

const handleEditAfterSales = (record: any) => {
  // 设置编辑数据并打开表单
  editingAfterSalesRecord.value = record
  afterSalesFormVisible.value = true
}

const handleAddEvaluation = () => {
  editingEvaluationRecord.value = null
  evaluationFormVisible.value = true
}

const handleEditEvaluation = (evaluation: any) => {
  console.log('编辑评价，接收到的评价数据:', evaluation)
  editingEvaluationRecord.value = evaluation
  evaluationFormVisible.value = true
}

const handleAfterSalesSuccess = (data?: any) => {
  console.log('售后记录成功处理:', data)
  const wasEditing = editingAfterSalesRecord.value !== null

  if (data) {
    if (wasEditing && editingAfterSalesRecord.value) {
      // 编辑模式：更新现有记录
      const currentRecord = editingAfterSalesRecord.value
      const updatedRecord = { ...currentRecord, ...data }
      console.log('调用售后更新方法:', updatedRecord)
      afterSalesTabRef.value?.updateRecord(updatedRecord)
    } else {
      // 新增模式：添加新记录
      console.log('调用售后添加方法:', data)
      afterSalesTabRef.value?.addRecord(data)
    }
  }

  // 刷新售后记录数据
  afterSalesTabRef.value?.refreshAfterSalesRecords()

  afterSalesFormVisible.value = false
  editingAfterSalesRecord.value = null
}

const handleEvaluationSuccess = (data?: any) => {
  console.log('服务评价成功处理:', data)

  if (data) {
    console.log('服务评价数据已更新:', data)
  }

  // 刷新服务评价数据
  if (serviceEvaluationTabRef.value) {
    console.log('刷新服务评价数据')
    serviceEvaluationTabRef.value.refreshEvaluation()
  } else {
    console.warn('serviceEvaluationTabRef 不存在')
  }

  evaluationFormVisible.value = false
  editingEvaluationRecord.value = null
}

const getServiceStatusText = (status: string): string => {
  const statusMap = {
    pending: '待派单',
    assigned: '已派单',
    in_service: '服务中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

const getPaymentStatusText = (status: string): string => {
  const statusMap = {
    unpaid: '未支付',
    paid: '已支付',
    partial: '部分支付'
  }
  return statusMap[status] || '未知'
}

// 监听visible和orderId变化，加载订单详情
watch(
  [() => props.visible, () => props.orderId],
  ([newVisible, newOrderId]) => {
    console.log('监听器触发 - visible:', newVisible, 'orderId:', newOrderId)
    if (newVisible && newOrderId) {
      console.log('开始加载订单详情，订单ID:', newOrderId)
      loadOrderDetail()
    }
  },
  { immediate: true }
)

const loadOrderDetail = async () => {
  if (props.orderId) {
    console.log('开始加载订单详情，订单ID:', props.orderId)
    await fetchOrderDetail(props.orderId)
  }
}
</script>

<style scoped lang="scss">
.order-view-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.order-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  margin-bottom: 0;

  .service-banner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .service-title {
      font-size: 20px;
      font-weight: bold;
    }

    .service-status {
      margin-left: auto;
      margin-right: 10px;
    }

    .edit-btn {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .order-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;

    .order-number {
      font-weight: 500;
    }

    .payment-status {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

.tab-navigation {
  background: #f5f5f5;
  padding: 0 20px;
  border-bottom: 1px solid #e4e7ed;

  :deep(.el-tabs) {
    .el-tabs__header {
      margin: 0;
    }

    .el-tabs__content {
      display: none;
    }
  }
}

.content-area {
  flex: 1;
  overflow: auto;
  padding: 20px;
}

.drawer-footer {
  padding: 20px;
  text-align: right;
  border-top: 1px solid #e4e7ed;
  background: white;
}
</style>
