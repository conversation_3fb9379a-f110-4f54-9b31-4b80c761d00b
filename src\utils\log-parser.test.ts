/**
 * 日志解析器测试文件
 * 用于验证各种日志格式的解析效果
 */

import { parseOperationLog, parseLogContent, formatFieldName } from './log-parser'

// 测试用例
const testCases = [
  {
    name: '新增收支记录JSON格式',
    input: JSON.stringify({
      operation: '新增收支记录',
      paymentAmount: '888',
      paymentRemark: '8989',
      paymentType: 'other',
      paymentTime: '2025-08-25T14:40:59.028'
    }),
    expected: {
      hasChanges: true,
      changesCount: 4
    }
  },
  {
    name: '订单信息更新文本格式',
    input: '单价: 599.00 → 空\n总金额: 599.00 → 空\n实际金额: 599.00 → 空',
    expected: {
      hasChanges: true,
      changesCount: 3
    }
  },
  {
    name: '包含changes字段的JSON格式',
    input: JSON.stringify({
      changes: {
        unitPrice: { old: '599.00', new: '' },
        totalAmount: { old: '599.00', new: '' },
        actualAmount: { old: '599.00', new: '' }
      }
    }),
    expected: {
      hasChanges: true,
      changesCount: 3
    }
  },
  {
    name: '空内容',
    input: '',
    expected: {
      hasChanges: false,
      changesCount: 0
    }
  }
]

// 运行测试
console.log('=== 日志解析器测试开始 ===')

testCases.forEach((testCase, index) => {
  console.log(`\n测试 ${index + 1}: ${testCase.name}`)
  console.log('输入:', testCase.input)

  try {
    const result = parseOperationLog(testCase.input)
    console.log('解析结果:', result)

    const passed =
      result.hasChanges === testCase.expected.hasChanges &&
      result.changes.length === testCase.expected.changesCount

    console.log(`测试结果: ${passed ? '✅ 通过' : '❌ 失败'}`)

    if (result.changes.length > 0) {
      console.log('变更详情:')
      result.changes.forEach((change) => {
        console.log(`  ${formatFieldName(change.field)}: ${change.oldValue} → ${change.newValue}`)
      })
    }
  } catch (error) {
    console.log('测试结果: ❌ 异常', error)
  }
})

console.log('\n=== 日志解析器测试结束 ===')

// 字段名称格式化测试
console.log('\n=== 字段名称格式化测试 ===')
const fieldTests = ['orderNo', 'customerName', 'paymentAmount', 'practitionerOneid', 'unknownField']

fieldTests.forEach((field) => {
  console.log(`${field} → ${formatFieldName(field)}`)
})
