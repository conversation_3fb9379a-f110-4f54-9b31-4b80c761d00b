<!--
  页面名称：编辑阿姨
  功能描述：编辑阿姨信息，包含基本信息、评级和资质文件管理
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="`编辑阿姨: ${practitioner?.name || ''}`"
    direction="rtl"
    size="700px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="edit-practitioner-form"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="formData.name" placeholder="请输入阿姨的真实姓名" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="formData.phone" placeholder="请输入手机号,用于登录" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="formData.idCard" placeholder="请输入18位身份证号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="籍贯" prop="hometown">
              <el-input v-model="formData.hometown" placeholder="例如:四川成都" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 服务信息 -->
      <div class="form-section">
        <h3 class="section-title">服务信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="主要服务类型" prop="serviceType">
              <el-select
                v-model="formData.serviceType"
                placeholder="请选择服务类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in serviceTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="从业年限" prop="experienceYears">
              <el-input v-model="formData.experienceYears" placeholder="请输入数字" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="平台状态" prop="platformStatus">
              <el-select
                v-model="formData.platformStatus"
                placeholder="请选择平台状态"
                style="width: 100%"
              >
                <el-option
                  v-for="item in platformStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评级" prop="rating">
              <el-input-number
                v-model="formData.rating"
                :min="1"
                :max="5"
                :precision="1"
                :step="0.1"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 资质文件管理 -->
      <div class="form-section">
        <h3 class="section-title">资质文件管理</h3>

        <!-- 身份证正反面 -->
        <div class="upload-section">
          <div class="upload-label">身份证正反面</div>
          <div class="upload-area">
            <div v-if="idCardFiles.length > 0" class="file-list">
              <div v-for="file in idCardFiles" :key="file.uid" class="file-item">
                <i class="fas fa-file-alt file-icon"></i>
                <span class="file-name">{{ file.name }}</span>
                <div class="file-actions">
                  <a href="#" @click.prevent="viewFile(file)">查看</a>
                  <a href="#" @click.prevent="deleteFile(file, 'idCard')">删除</a>
                </div>
              </div>
            </div>
            <el-upload
              ref="idCardUpload"
              :before-upload="beforeIdCardUpload"
              :http-request="handleIdCardUpload"
              :on-remove="onIdCardRemove"
              :file-list="[]"
              multiple
              accept=".jpg,.jpeg,.png,.pdf"
            >
              <el-button type="primary" plain>选择文件</el-button>
            </el-upload>
            <div class="upload-tip">支持图片和PDF格式，可多选</div>
          </div>
        </div>

        <!-- 健康证 -->
        <div class="upload-section">
          <div class="upload-label">健康证</div>
          <div class="upload-area">
            <div v-if="healthCertFiles.length > 0" class="file-list">
              <div v-for="file in healthCertFiles" :key="file.uid" class="file-item">
                <i class="fas fa-file-alt file-icon"></i>
                <span class="file-name">{{ file.name }}</span>
                <div class="file-actions">
                  <a href="#" @click.prevent="viewFile(file)">查看</a>
                  <a href="#" @click.prevent="deleteFile(file, 'healthCert')">删除</a>
                </div>
              </div>
            </div>
            <el-upload
              ref="healthCertUpload"
              :before-upload="beforeHealthCertUpload"
              :http-request="handleHealthCertUpload"
              :on-remove="onHealthCertRemove"
              :file-list="[]"
              accept=".jpg,.jpeg,.png,.pdf"
            >
              <el-button type="primary" plain>选择文件</el-button>
            </el-upload>
            <div class="upload-tip">支持图片和PDF格式</div>
          </div>
        </div>

        <!-- 专业技能证书 -->
        <div class="upload-section">
          <div class="upload-label">专业技能证书 (可多选)</div>
          <div class="upload-area">
            <div v-if="skillCertFiles.length > 0" class="file-list">
              <div v-for="file in skillCertFiles" :key="file.uid" class="file-item">
                <i class="fas fa-file-alt file-icon"></i>
                <span class="file-name">{{ file.name }}</span>
                <div class="file-actions">
                  <a href="#" @click.prevent="viewFile(file)">查看</a>
                  <a href="#" @click.prevent="deleteFile(file, 'skillCert')">删除</a>
                </div>
              </div>
            </div>
            <el-upload
              ref="skillCertUpload"
              :before-upload="beforeSkillCertUpload"
              :http-request="handleSkillCertUpload"
              :on-remove="onSkillCertRemove"
              :file-list="[]"
              multiple
              accept=".jpg,.jpeg,.png,.pdf"
            >
              <el-button type="primary" plain>选择文件</el-button>
            </el-upload>
            <div class="upload-tip">支持图片和PDF格式，可多选</div>
          </div>
        </div>

        <!-- 其他附件 -->
        <div class="upload-section">
          <div class="upload-label">其他附件</div>
          <div class="upload-area">
            <div v-if="otherFiles.length > 0" class="file-list">
              <div v-for="file in otherFiles" :key="file.uid" class="file-item">
                <i class="fas fa-file-alt file-icon"></i>
                <span class="file-name">{{ file.name }}</span>
                <div class="file-actions">
                  <a href="#" @click.prevent="viewFile(file)">查看</a>
                  <a href="#" @click.prevent="deleteFile(file, 'other')">删除</a>
                </div>
              </div>
            </div>
            <el-upload
              ref="otherUpload"
              :before-upload="beforeOtherUpload"
              :http-request="handleOtherUpload"
              :on-remove="onOtherRemove"
              :file-list="[]"
              multiple
              accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
            >
              <el-button type="primary" plain>选择文件</el-button>
            </el-upload>
            <div class="upload-tip">支持图片、PDF、Word文档格式，可多选</div>
          </div>
        </div>
      </div>
    </el-form>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting"> 保存更新 </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits, watch, onMounted } from 'vue'
import type { FormInstance, UploadProps } from 'element-plus'
import { ElMessage } from 'element-plus'
import { updatePractitioner, getPractitionerDetail } from '@/api/mall/employment/practitioner'
import { getDictDataPage } from '@/api/system/dict/dict.data'
import { updateFile } from '@/api/infra/file'

const props = defineProps<{
  visible: boolean
  practitioner: any
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 表单引用
const formRef = ref<FormInstance>()

// 提交状态
const submitting = ref(false)

// 表单数据
const formData = reactive({
  id: 0,
  name: '',
  phone: '',
  idCard: '',
  hometown: '',
  serviceType: '',
  experienceYears: '',
  platformStatus: 'cooperating',
  rating: 4.5
})

// 下拉选项数据
const serviceTypeOptions = ref<any[]>([])
const platformStatusOptions = ref<any[]>([])

// 文件列表
const idCardFiles = ref<any[]>([])
const healthCertFiles = ref<any[]>([])
const skillCertFiles = ref<any[]>([])
const otherFiles = ref<any[]>([])

// 上传配置 - 使用自定义上传方法

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入阿姨姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  idCard: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    { pattern: /^\d{17}[\dXx]$/, message: '请输入正确的身份证号', trigger: 'blur' }
  ],
  hometown: [{ required: true, message: '请输入籍贯', trigger: 'blur' }],
  serviceType: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
  experienceYears: [
    { required: true, message: '请输入从业年限', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入数字', trigger: 'blur' }
  ],
  platformStatus: [{ required: true, message: '请选择平台状态', trigger: 'change' }],
  rating: [{ required: true, message: '请输入评级', trigger: 'blur' }]
}

/** 加载数据字典选项 */
const loadDictOptions = async () => {
  try {
    console.log('[EditPractitioner] 开始加载数据字典选项')

    // 加载服务类型选项
    console.log('[EditPractitioner] 加载服务类型选项')
    const serviceTypeRes = await getDictDataPage({
      dictType: 'service_type',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    console.log('[EditPractitioner] 服务类型响应:', serviceTypeRes)

    serviceTypeOptions.value = (serviceTypeRes.data?.list || serviceTypeRes.list || []).map(
      (item: any) => ({
        label: item.label,
        value: item.value
      })
    )
    console.log('[EditPractitioner] 服务类型选项:', serviceTypeOptions.value)

    // 加载平台状态选项
    console.log('[EditPractitioner] 加载平台状态选项')
    const platformStatusRes = await getDictDataPage({
      dictType: 'platform_status',
      status: 0,
      pageNo: 1,
      pageSize: 99
    })
    console.log('[EditPractitioner] 平台状态响应:', platformStatusRes)

    platformStatusOptions.value = (
      platformStatusRes.data?.list ||
      platformStatusRes.list ||
      []
    ).map((item: any) => ({
      label: item.label,
      value: item.value
    }))
    console.log('[EditPractitioner] 平台状态选项:', platformStatusOptions.value)
  } catch (error) {
    console.error('[EditPractitioner] 加载数据字典失败:', error)
  }
}

// 监听practitioner变化，填充表单数据
watch(
  () => props.practitioner,
  async (newPractitioner) => {
    console.log('[EditPractitioner] practitioner变化:', newPractitioner)
    if (newPractitioner && props.visible) {
      await loadPractitionerData(newPractitioner.id)
    }
  },
  { immediate: true }
)

// 监听visible变化
watch(
  () => props.visible,
  async (visible) => {
    console.log('[EditPractitioner] visible变化:', visible)
    if (visible && props.practitioner) {
      await loadPractitionerData(props.practitioner.id)
    }
  }
)

// 加载阿姨数据
const loadPractitionerData = async (id: number) => {
  try {
    console.log('[EditPractitioner] 开始获取阿姨详情，ID:', id)
    // 获取阿姨详情
    const detailRes = await getPractitionerDetail(id)
    console.log('[EditPractitioner] 详情API响应:', detailRes)
    
    // 处理不同的响应格式
    let detail = null
    if (detailRes && detailRes.data) {
      // 如果响应包含data字段
      detail = detailRes.data
    } else if (detailRes && detailRes.id) {
      // 如果响应直接是详情对象
      detail = detailRes
    }
    
    if (detail) {
      console.log('[EditPractitioner] 处理后的详情数据:', detail)
      
      Object.assign(formData, {
        id: detail.id,
        name: detail.name || '',
        phone: detail.phone || '',
        idCard: detail.idCard || '',
        hometown: detail.hometown || '',
        serviceType: detail.serviceType || '',
        experienceYears: detail.experienceYears?.toString() || '',
        platformStatus: detail.platformStatus || 'cooperating',
        rating: detail.rating || 4.5
      })

      // 加载资质文件
      loadQualifications(detail.qualifications || [])
      
      console.log('[EditPractitioner] 表单数据已填充:', formData)
    } else {
      console.error('[EditPractitioner] 无法解析详情数据')
      ElMessage.error('获取阿姨详情失败')
    }
  } catch (error) {
    console.error('获取阿姨详情失败:', error)
    ElMessage.error('获取阿姨详情失败')
  }
}

// 加载资质文件
const loadQualifications = (qualifications: any[]) => {
  console.log('[EditPractitioner] 开始加载资质文件:', qualifications)

  idCardFiles.value = []
  healthCertFiles.value = []
  skillCertFiles.value = []
  otherFiles.value = []

  qualifications.forEach((qual) => {
    console.log('[EditPractitioner] 处理文件:', qual.fileType, qual.fileName)
    const file = {
      uid: qual.id,
      name: qual.fileName,
      url: qual.fileUrl,
      size: qual.fileSize
    }

    switch (qual.fileType) {
      case 'id_card':
        idCardFiles.value.push(file)
        break
      case 'health_cert':
        healthCertFiles.value.push(file)
        break
      case 'skill_cert':
        skillCertFiles.value.push(file)
        break
      case 'other':
        otherFiles.value.push(file)
        break
      default:
        console.warn('[EditPractitioner] 未知文件类型:', qual.fileType)
        otherFiles.value.push(file)
        break
    }
  })

  console.log('[EditPractitioner] 资质文件加载结果:', {
    idCardFiles: idCardFiles.value.length,
    healthCertFiles: healthCertFiles.value.length,
    skillCertFiles: skillCertFiles.value.length,
    otherFiles: otherFiles.value.length
  })
}

// 文件上传处理
const beforeIdCardUpload: UploadProps['beforeUpload'] = (file) => {
  const isValidType = ['image/jpeg', 'image/png', 'application/pdf'].includes(file.type)
  if (!isValidType) {
    ElMessage.error('只能上传图片或PDF格式文件')
    return false
  }
  return true
}

const handleIdCardUpload = async (options: any) => {
  try {
    const uploadFormData = new FormData()
    uploadFormData.append('file', options.file)

    const response = await updateFile(uploadFormData)
    if (response && response.data) {
      const file = options.file
      file.url = response.data
      // 新上传的文件不设置uid，让后端自动生成
      file.uid = undefined
      idCardFiles.value.push(file)
      ElMessage.success('身份证文件上传成功')
    }
  } catch (error) {
    console.error('身份证文件上传失败:', error)
    ElMessage.error('身份证文件上传失败')
  }
}

const onIdCardRemove = (file: any) => {
  const index = idCardFiles.value.findIndex((item) => item.uid === file.uid)
  if (index > -1) {
    idCardFiles.value.splice(index, 1)
  }
}

const beforeHealthCertUpload: UploadProps['beforeUpload'] = (file) => {
  const isValidType = ['image/jpeg', 'image/png', 'application/pdf'].includes(file.type)
  if (!isValidType) {
    ElMessage.error('只能上传图片或PDF格式文件')
    return false
  }
  return true
}

const handleHealthCertUpload = async (options: any) => {
  try {
    const uploadFormData = new FormData()
    uploadFormData.append('file', options.file)

    const response = await updateFile(uploadFormData)
    if (response && response.data) {
      const file = options.file
      file.url = response.data
      // 新上传的文件不设置uid，让后端自动生成
      file.uid = undefined
      healthCertFiles.value.push(file)
      ElMessage.success('健康证文件上传成功')
    }
  } catch (error) {
    console.error('健康证文件上传失败:', error)
    ElMessage.error('健康证文件上传失败')
  }
}

const onHealthCertRemove = (file: any) => {
  const index = healthCertFiles.value.findIndex((item) => item.uid === file.uid)
  if (index > -1) {
    healthCertFiles.value.splice(index, 1)
  }
}

const beforeSkillCertUpload: UploadProps['beforeUpload'] = (file) => {
  const isValidType = ['image/jpeg', 'image/png', 'application/pdf'].includes(file.type)
  if (!isValidType) {
    ElMessage.error('只能上传图片或PDF格式文件')
    return false
  }
  return true
}

const handleSkillCertUpload = async (options: any) => {
  try {
    const uploadFormData = new FormData()
    uploadFormData.append('file', options.file)

    const response = await updateFile(uploadFormData)
    if (response && response.data) {
      const file = options.file
      file.url = response.data
      // 新上传的文件不设置uid，让后端自动生成
      file.uid = undefined
      skillCertFiles.value.push(file)
      ElMessage.success('技能证书文件上传成功')
    }
  } catch (error) {
    console.error('技能证书文件上传失败:', error)
    ElMessage.error('技能证书文件上传失败')
  }
}

const onSkillCertRemove = (file: any) => {
  const index = skillCertFiles.value.findIndex((item) => item.uid === file.uid)
  if (index > -1) {
    skillCertFiles.value.splice(index, 1)
  }
}

const beforeOtherUpload: UploadProps['beforeUpload'] = (file) => {
  const isValidType = [
    'image/jpeg',
    'image/png',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ].includes(file.type)
  if (!isValidType) {
    ElMessage.error('只能上传图片、PDF或Word文档格式文件')
    return false
  }
  return true
}

const handleOtherUpload = async (options: any) => {
  try {
    const uploadFormData = new FormData()
    uploadFormData.append('file', options.file)

    const response = await updateFile(uploadFormData)
    if (response && response.data) {
      const file = options.file
      file.url = response.data
      // 新上传的文件不设置uid，让后端自动生成
      file.uid = undefined
      otherFiles.value.push(file)
      ElMessage.success('其他附件文件上传成功')
    }
  } catch (error) {
    console.error('其他附件文件上传失败:', error)
    ElMessage.error('其他附件文件上传失败')
  }
}

const onOtherRemove = (file: any) => {
  const index = otherFiles.value.findIndex((item) => item.uid === file.uid)
  if (index > -1) {
    otherFiles.value.splice(index, 1)
  }
}

// 查看文件
const viewFile = (file: any) => {
  if (file.url) {
    window.open(file.url, '_blank')
  }
}

// 删除文件
const deleteFile = (file: any, type: string) => {
  switch (type) {
    case 'idCard':
      onIdCardRemove(file)
      break
    case 'healthCert':
      onHealthCertRemove(file)
      break
    case 'skillCert':
      onSkillCertRemove(file)
      break
    case 'other':
      onOtherRemove(file)
      break
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 构建资质文件列表
    const qualifications = []

    // 身份证文件
    idCardFiles.value.forEach((file) => {
      const qualification: any = {
        fileType: 'id_card',
        fileName: file.name,
        fileUrl: file.url,
        fileSize: file.size,
        fileExtension: file.name.split('.').pop(),
        sortOrder: 0,
        status: 1
      }
      // 只有已存在的文件才传递ID
      if (file.uid) {
        qualification.id = file.uid
      }
      qualifications.push(qualification)
    })

    // 健康证文件
    healthCertFiles.value.forEach((file) => {
      const qualification: any = {
        fileType: 'health_cert',
        fileName: file.name,
        fileUrl: file.url,
        fileSize: file.size,
        fileExtension: file.name.split('.').pop(),
        sortOrder: 0,
        status: 1
      }
      // 只有已存在的文件才传递ID
      if (file.uid) {
        qualification.id = file.uid
      }
      qualifications.push(qualification)
    })

    // 技能证书文件
    skillCertFiles.value.forEach((file) => {
      const qualification: any = {
        fileType: 'skill_cert',
        fileName: file.name,
        fileUrl: file.url,
        fileSize: file.size,
        fileExtension: file.name.split('.').pop(),
        sortOrder: 0,
        status: 1
      }
      // 只有已存在的文件才传递ID
      if (file.uid) {
        qualification.id = file.uid
      }
      qualifications.push(qualification)
    })

    // 其他附件文件
    otherFiles.value.forEach((file) => {
      const qualification: any = {
        fileType: 'other',
        fileName: file.name,
        fileUrl: file.url,
        fileSize: file.size,
        fileExtension: file.name.split('.').pop(),
        sortOrder: 0,
        status: 1
      }
      // 只有已存在的文件才传递ID
      if (file.uid) {
        qualification.id = file.uid
      }
      qualifications.push(qualification)
    })

    const data = {
      ...formData,
      experienceYears: parseInt(formData.experienceYears),
      qualifications
    }

    await updatePractitioner(data)
    emit('success')
    handleClose()
  } catch (error) {
    console.error('更新阿姨失败:', error)
    ElMessage.error('更新阿姨失败')
  } finally {
    submitting.value = false
  }
}

// 初始化
onMounted(() => {
  loadDictOptions()
})
</script>

<style scoped lang="scss">
.edit-practitioner-form {
  .form-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }
  }

  .upload-section {
    margin-bottom: 20px;

    .upload-label {
      font-weight: 500;
      margin-bottom: 10px;
      color: #333;
    }

    .upload-area {
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      padding: 20px;
      text-align: center;
      background: #fafafa;

      &:hover {
        border-color: #409eff;
      }

      .file-list {
        margin-bottom: 15px;
        text-align: left;

        .file-item {
          display: flex;
          align-items: center;
          padding: 8px;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          margin-bottom: 8px;
          background: white;

          .file-icon {
            margin-right: 8px;
            color: #666;
          }

          .file-name {
            flex: 1;
            color: #333;
          }

          .file-actions {
            a {
              margin-left: 10px;
              color: #409eff;
              text-decoration: none;

              &:hover {
                text-decoration: underline;
              }
            }
          }
        }
      }

      .upload-tip {
        margin-top: 10px;
        color: #666;
        font-size: 12px;
      }
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
