<!--
  页面名称：收支记录标签页
  功能描述：展示家政服务订单的收支记录
-->
<template>
  <div class="income-expense-tab">
    <div class="section-header">
      <div class="section-title">
        <el-icon><TrendCharts /></el-icon>
        收支记录
      </div>
      <div class="header-actions">
        <el-button type="primary" size="small" @click="handleAddRecord">
          <el-icon><Plus /></el-icon>
          新增收支记录
        </el-button>
      </div>
    </div>

    <div class="record-content">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      <div v-else-if="incomeExpenseRecords.length > 0" class="records-table">
        <el-table :data="incomeExpenseRecords" style="width: 100%" size="small">
          <el-table-column prop="recordType" label="类型" width="120">
            <template #default="scope">
              <el-tag :type="getRecordTypeTag(scope.row.recordType)" size="small">
                {{ getRecordTypeText(scope.row.recordType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="金额" width="100">
            <template #default="scope">
              <span :class="getAmountClass(scope.row.recordType)"
                >¥{{ formatAmount(scope.row.amount) }}</span
              >
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="日期" width="120">
            <template #default="scope">
              {{ formatDate(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="200" />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button type="warning" size="small" @click="handleEditRecord(scope.row)">
                编辑
              </el-button>
              <el-button type="danger" size="small" @click="handleDeleteRecord(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else class="no-records">
        <el-empty description="暂无收支记录" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { TrendCharts, Plus } from '@element-plus/icons-vue'
import { HousekeepingServiceOrderApi } from '@/api/OrderCenter/HousekeepingServiceOrder'

interface Props {
  orderDetail: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'add-record': []
  'edit-record': [record: any]
}>()

// 响应式数据
const loading = ref(false)
const incomeExpenseRecords = ref<any[]>([])

// 获取收支记录
const fetchIncomeExpenseRecords = async () => {
  if (!props.orderDetail?.id) return

  loading.value = true
  try {
    const result = await HousekeepingServiceOrderApi.getIncomeExpenseList(props.orderDetail.id)
    // 修复数据结构处理
    if (result && result.records) {
      incomeExpenseRecords.value = result.records
    } else if (result && result.data && result.data.records) {
      incomeExpenseRecords.value = result.data.records
    } else {
      incomeExpenseRecords.value = []
    }
    console.log('收支记录获取成功:', result)
  } catch (error) {
    console.error('获取收支记录失败:', error)
    ElMessage.error('获取收支记录失败')
    incomeExpenseRecords.value = []
  } finally {
    loading.value = false
  }
}

// 监听订单详情变化
watch(
  () => props.orderDetail,
  (newVal) => {
    if (newVal?.id) {
      fetchIncomeExpenseRecords()
    }
  },
  { immediate: true }
)

// 格式化金额
const formatAmount = (amount: number): string => {
  if (!amount) return '0'
  return amount.toLocaleString()
}

// 获取记录类型文本
const getRecordTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    extra_income: '额外收入',
    compensation_expense: '赔偿支出',
    service_income: '服务收入',
    extra_expense: '额外支出',
    order_income: '订单收入',
    order_expense: '订单支出',
    refund_income: '退款收入',
    refund_expense: '退款支出',
    额外收入: '额外收入',
    赔偿支出: '赔偿支出',
    服务收入: '服务收入',
    额外支出: '额外支出',
    订单收入: '订单收入',
    订单支出: '订单支出',
    退款收入: '退款收入',
    退款支出: '退款支出'
  }
  return typeMap[type] || type
}

// 获取记录类型标签样式
const getRecordTypeTag = (type: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const tagMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    extra_income: 'success',
    compensation_expense: 'danger',
    service_income: 'primary',
    extra_expense: 'warning',
    order_income: 'success',
    order_expense: 'danger',
    refund_income: 'success',
    refund_expense: 'danger',
    额外收入: 'success',
    赔偿支出: 'danger',
    服务收入: 'primary',
    额外支出: 'warning',
    订单收入: 'success',
    订单支出: 'danger',
    退款收入: 'success',
    退款支出: 'danger'
  }
  return tagMap[type] || 'info'
}

// 格式化日期
const formatDate = (timestamp: number): string => {
  if (!timestamp) return '-'
  try {
    const date = new Date(timestamp)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return '-'
  }
}

// 获取金额样式类
const getAmountClass = (type: string): string => {
  const classMap: Record<string, string> = {
    extra_income: 'income-amount',
    compensation_expense: 'expense-amount',
    service_income: 'income-amount',
    extra_expense: 'expense-amount',
    order_income: 'income-amount',
    order_expense: 'expense-amount',
    refund_income: 'income-amount',
    refund_expense: 'expense-amount',
    额外收入: 'income-amount',
    赔偿支出: 'expense-amount',
    服务收入: 'income-amount',
    额外支出: 'expense-amount',
    订单收入: 'income-amount',
    订单支出: 'expense-amount',
    退款收入: 'income-amount',
    退款支出: 'expense-amount'
  }
  return classMap[type] || ''
}

// 新增收支记录
const handleAddRecord = () => {
  emit('add-record')
}

// 编辑收支记录
const handleEditRecord = (record: any) => {
  emit('edit-record', record)
}

// 删除收支记录
const handleDeleteRecord = async (record: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这条收支记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await HousekeepingServiceOrderApi.deleteIncomeExpenseRecord(record.id)
    ElMessage.success('删除成功')
    fetchIncomeExpenseRecords() // 刷新列表
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除收支记录失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 组件挂载时获取数据
// 移除重复的onMounted钩子，避免重复调用
// onMounted(() => {
//   if (props.orderDetail?.id) {
//     fetchIncomeExpenseList()
//   }
// })
</script>

<style scoped lang="scss">
.income-expense-tab {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .section-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      color: #303133;

      .el-icon {
        margin-right: 8px;
        color: #409eff;
      }
    }
  }

  .record-content {
    .loading-container {
      padding: 20px;
    }

    .records-table {
      .income-amount {
        color: #67c23a;
        font-weight: 500;
      }

      .expense-amount {
        color: #f56c6c;
        font-weight: 500;
      }
    }

    .no-records {
      padding: 40px 0;
      text-align: center;
    }
  }
}
</style>
