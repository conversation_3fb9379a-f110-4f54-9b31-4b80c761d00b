<!--
  页面名称：收款信息标签页
  功能描述：展示家政服务订单的收款信息
-->
<template>
  <div class="payment-info-tab">
    <div class="payment-content">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>
      <div v-else-if="paymentInfo" class="payment-details">
        <el-row :gutter="40">
          <el-col :span="12">
            <div class="detail-item">
              <label>订单金额：</label>
              <span class="amount">¥{{ formatAmount(paymentInfo.totalAmount) }}</span>
            </div>
            <div class="detail-item">
              <label>收款方式：</label>
              <span>{{ getPaymentTypeText(paymentInfo.paymentRecords?.[0]?.paymentType) }}</span>
            </div>
            <div class="detail-item">
              <label>收款备注：</label>
              <span>{{ paymentInfo.paymentRecords?.[0]?.paymentRemark || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>支付状态：</label>
              <el-tag
                :type="getPaymentStatusType(paymentInfo.paymentRecords?.[0]?.paymentStatus)"
                size="small"
                >{{ getPaymentStatusText(paymentInfo.paymentRecords?.[0]?.paymentStatus) }}</el-tag
              >
            </div>
            <div class="detail-item">
              <label>收款日期：</label>
              <span>{{ formatDateTime(paymentInfo.paymentRecords?.[0]?.paymentTime) }}</span>
            </div>
            <div class="detail-item">
              <label>收款金额：</label>
              <span class="amount">¥{{ formatAmount(paymentInfo.totalPaidAmount) }}</span>
            </div>
            <div class="detail-item">
              <label>操作人：</label>
              <span>{{ paymentInfo.paymentRecords?.[0]?.operatorName || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
      <div v-else class="no-payment-info">
        <el-empty description="暂无收款信息" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Money } from '@element-plus/icons-vue'
import { HousekeepingServiceOrderApi } from '@/api/OrderCenter/HousekeepingServiceOrder'

interface Props {
  orderDetail: any
}

const props = defineProps<Props>()

// 响应式数据
const loading = ref(false)
const paymentInfo = ref<any>(null)

// 获取收款信息
const fetchPaymentInfo = async () => {
  if (!props.orderDetail?.id) return

  loading.value = true
  try {
    const result = await HousekeepingServiceOrderApi.getPaymentInfo(props.orderDetail.id)
    paymentInfo.value = result
    console.log('收款信息获取成功:', result)
  } catch (error) {
    console.error('获取收款信息失败:', error)
    ElMessage.error('获取收款信息失败')
    paymentInfo.value = null
  } finally {
    loading.value = false
  }
}

// 监听订单详情变化
watch(
  () => props.orderDetail,
  (newVal) => {
    if (newVal?.id) {
      fetchPaymentInfo()
    }
  },
  { immediate: true }
)

// 格式化金额
const formatAmount = (amount: number): string => {
  if (!amount) return '0.00'
  return amount.toFixed(2)
}

// 格式化日期时间
const formatDateTime = (timestamp: number): string => {
  if (!timestamp) return '-'
  try {
    const date = new Date(timestamp)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return '-'
  }
}

// 获取支付状态类型
const getPaymentStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    success: 'success',
    pending: 'warning',
    failed: 'danger',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取支付状态文本
const getPaymentStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    success: '已支付',
    pending: '处理中',
    failed: '失败',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 获取支付方式文本
const getPaymentTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    cash: '现金',
    wechat: '微信支付',
    alipay: '支付宝',
    bank_transfer: '银行转账',
    pos: 'POS机刷卡',
    other: '其他'
  }
  return typeMap[type] || type
}

// 组件挂载时获取数据
// 移除重复的onMounted钩子，避免重复调用
// onMounted(() => {
//   if (props.orderDetail?.id) {
//     fetchPaymentInfo()
//   }
// })
</script>

<style scoped lang="scss">
.payment-info-tab {
  .payment-content {
    .loading-container {
      padding: 20px;
    }

    .payment-details {
      .detail-item {
        margin-bottom: 16px;
        display: flex;
        align-items: center;

        label {
          min-width: 80px;
          color: #606266;
          font-weight: 500;
          margin-right: 8px;
        }

        span {
          color: #303133;
          flex: 1;

          &.amount {
            color: #f56c6c;
            font-weight: 500;
            font-size: 16px;
          }
        }
      }
    }

    .no-payment-info {
      padding: 40px 0;
      text-align: center;
    }
  }
}
</style>
