<!--
  页面名称：高校实践订单操作日志
  功能描述：展示高校实践订单的操作日志，支持变更内容解析，数据没有发生改变则不展示
-->
<template>
  <el-drawer
    :model-value="visible"
    :title="`${orderData?.orderNo || ''} - 操作日志`"
    direction="rtl"
    size="700px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @update:model-value="handleUpdateVisible"
  >
    <div class="optlog-container">
      <!-- 搜索筛选 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" @submit.prevent="handleSearch">
          <el-form-item label="日志类型">
            <el-select v-model="searchForm.logType" placeholder="请选择日志类型" clearable>
              <el-option label="全部" value="" />
              <el-option label="订单创建" value="订单创建" />
              <el-option label="订单编辑" value="订单编辑" />
              <el-option label="审批通过" value="审批通过" />
              <el-option label="审批驳回" value="审批驳回" />
              <el-option label="确认收款" value="确认收款" />
              <el-option label="订单完成" value="订单完成" />
              <el-option label="系统操作" value="系统管理员" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 日志内容 -->
      <el-timeline v-else>
        <el-timeline-item
          v-for="(log, index) in filteredLogList"
          :key="log.id || index"
          :timestamp="formatDateTime(log.createTime)"
          placement="top"
          :color="getLogColor(log.logType)"
        >
          <div class="log-item">
            <div class="log-header">
              <span class="log-user">{{ log.operatorName }} ({{ log.operatorRole }})</span>
              <el-button :type="getButtonType(log.logType) as any" size="small" class="log-button">
                {{ getButtonText(log.logType) }}
              </el-button>
            </div>

            <div class="log-content">
              <div class="log-action">{{ log.logTitle }}</div>

              <!-- 状态变更 -->
              <div
                v-if="log.oldStatus && log.newStatus && log.oldStatus !== log.newStatus"
                class="log-changes"
              >
                <div class="change-item">
                  <span class="change-field">状态变更:</span>
                  <span class="change-old">{{ getStatusText(log.oldStatus) }}</span>
                  <span class="change-arrow">→</span>
                  <span class="change-new">{{ getStatusText(log.newStatus) }}</span>
                </div>
              </div>

              <!-- 字段变更详情 -->
              <div
                v-if="parsedLogs[log.id]?.hasChanges && log.logType !== '订单创建'"
                class="field-changes"
              >
                <div class="changes-title">字段变更详情:</div>
                <div
                  v-for="change in parsedLogs[log.id].changes"
                  :key="change.field"
                  class="field-change-item"
                >
                  <span class="field-name">{{ formatFieldName(change.field) }}:</span>
                  <span class="field-old">{{
                    formatFieldValue(change.oldValue, change.field)
                  }}</span>
                  <span class="change-arrow">→</span>
                  <span class="field-new">{{
                    formatFieldValue(change.newValue, change.field)
                  }}</span>
                </div>
              </div>

              <!-- 订单创建日志：隐藏JSON内容，不展示 -->
              <!-- <div v-if="log.logType === '订单创建' && log.logContent" class="log-json-content">
                <div class="json-title">创建内容:</div>
                <pre class="json-content">{{ formatJsonContent(log.logContent) }}</pre>
              </div> -->

              <!-- 其他日志内容（如果没有字段变更则显示原始内容） -->
              <div
                v-if="
                  !parsedLogs[log.id]?.hasChanges && log.logContent && log.logType !== '订单创建'
                "
                class="log-content-text"
              >
                {{ log.logContent }}
              </div>

              <!-- 关联方信息 -->
              <div v-if="log.relatedPartyType && log.relatedPartyName" class="related-party">
                <span class="related-label">关联方:</span>
                <span class="related-value"
                  >{{ log.relatedPartyType }} - {{ log.relatedPartyName }}</span
                >
              </div>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>

      <!-- 空状态 -->
      <div v-if="!loading && filteredLogList.length === 0" class="empty-state">
        <el-empty description="暂无操作日志" />
      </div>

      <!-- 分页 -->
      <div v-if="!loading && pagination.total > 0" class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import type {
  UniversityPracticeOrder,
  OperationLogItem
} from '@/api/OrderCenter/UniversityPracticeCenter'
import { UniversityPracticeOrderApi } from '@/api/OrderCenter/UniversityPracticeCenter'
import { parseLogContent, formatFieldName, formatFieldValue } from '@/utils/log-parser'

// Props
interface Props {
  visible: boolean
  orderData?: UniversityPracticeOrder | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const loading = ref(false)
const logList = ref<OperationLogItem[]>([])
const parsedLogs = ref<Record<number, any>>({})

// 搜索表单
const searchForm = ref({
  logType: '',
  dateRange: [] as string[]
})

// 分页信息
const pagination = ref({
  page: 1,
  size: 20,
  total: 0
})

// 计算属性：过滤后的日志列表
const filteredLogList = computed(() => {
  let filtered = logList.value

  // 按日志类型过滤
  if (searchForm.value.logType) {
    filtered = filtered.filter((log) => log.logType === searchForm.value.logType)
  }

  // 按时间范围过滤
  if (searchForm.value.dateRange && searchForm.value.dateRange.length === 2) {
    const [startDate, endDate] = searchForm.value.dateRange
    filtered = filtered.filter((log) => {
      const logDate = new Date(log.createTime)
      const start = new Date(startDate)
      const end = new Date(endDate)
      end.setHours(23, 59, 59, 999) // 结束日期包含当天
      return logDate >= start && logDate <= end
    })
  }

  return filtered
})

// 监听抽屉显示状态，获取日志数据
watch(
  () => props.visible,
  (newVal) => {
    if (newVal && props.orderData) {
      fetchOperationLogs()
    }
  }
)

// 获取操作日志
const fetchOperationLogs = async () => {
  if (!props.orderData?.id || !props.orderData?.orderNo) {
    ElMessage.error('订单信息不完整')
    return
  }

  loading.value = true
  try {
    const params = {
      orderId: props.orderData.id,
      orderNo: props.orderData.orderNo,
      logType: searchForm.value.logType || undefined,
      startDate: searchForm.value.dateRange?.[0] || undefined,
      endDate: searchForm.value.dateRange?.[1] || undefined,
      page: pagination.value.page,
      size: pagination.value.size
    }

    const result = await UniversityPracticeOrderApi.getOperationLogs(params)
    logList.value = result.list || []
    pagination.value.total = result.total || 0

    // 解析每条日志的内容
    parseAllLogs()
  } catch (error) {
    console.error('获取操作日志失败:', error)
    ElMessage.error('获取操作日志失败')
    logList.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

// 解析所有日志内容
const parseAllLogs = () => {
  parsedLogs.value = {}
  logList.value.forEach((log) => {
    // 对于订单创建日志，不进行解析，直接显示JSON内容
    if (log.logContent && log.logType !== '订单创建') {
      parsedLogs.value[log.id] = parseLogContent(log.logContent)
    }
  })
}

// 搜索
const handleSearch = () => {
  pagination.value.page = 1
  fetchOperationLogs()
}

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    logType: '',
    dateRange: []
  }
  pagination.value.page = 1
  fetchOperationLogs()
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.value.size = size
  pagination.value.page = 1
  fetchOperationLogs()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.value.page = page
  fetchOperationLogs()
}

// 格式化JSON内容
const formatJsonContent = (jsonString: string) => {
  try {
    // 尝试解析JSON并格式化
    const jsonObj = JSON.parse(jsonString)
    return JSON.stringify(jsonObj, null, 2)
  } catch (error) {
    // 如果解析失败，直接返回原内容
    return jsonString
  }
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hour = date.getHours().toString().padStart(2, '0')
  const minute = date.getMinutes().toString().padStart(2, '0')
  return `${year}/${month}/${day} ${hour}:${minute}`
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    // 订单状态
    draft: '草稿',
    pending_approval: '待审批',
    approving: '审批中',
    approved: '已批准',
    rejected: '已拒绝',
    pending_payment: '待支付',
    executing: '执行中',
    completed: '已完成',
    cancelled: '已取消',
    // 支付状态
    pending: '待支付',
    paid: '已支付',
    refunded: '已退款'
  }
  return statusMap[status] || status
}

// 获取日志颜色
const getLogColor = (logType: string) => {
  const colorMap: Record<string, string> = {
    订单创建: '#67C23A',
    订单编辑: '#E6A23C',
    审批通过: '#409EFF',
    审批驳回: '#F56C6C',
    确认收款: '#67C23A',
    订单完成: '#67C23A',
    系统管理员: '#909399'
  }
  return colorMap[logType] || '#909399'
}

// 获取按钮类型
const getButtonType = (logType: string) => {
  const typeMap: Record<string, string> = {
    订单创建: 'success',
    订单编辑: 'warning',
    审批通过: 'primary',
    审批驳回: 'danger',
    确认收款: 'success',
    订单完成: 'success',
    系统管理员: 'info'
  }
  return typeMap[logType] || 'info'
}

// 获取按钮文本
const getButtonText = (logType: string) => {
  const textMap: Record<string, string> = {
    订单创建: '创建',
    订单编辑: '编辑',
    审批通过: '审批通过',
    审批驳回: '审批驳回',
    确认收款: '收款确认',
    订单完成: '完成',
    系统管理员: '系统'
  }
  return textMap[logType] || logType
}

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false)
}

// 处理抽屉显示状态更新
const handleUpdateVisible = (value: boolean) => {
  emit('update:visible', value)
}

// 组件挂载时初始化
onMounted(() => {
  if (props.visible && props.orderData) {
    fetchOperationLogs()
  }
})
</script>

<style scoped lang="scss">
.optlog-container {
  padding: 20px;
  height: calc(100vh - 200px);
  overflow-y: auto;
}

.search-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;

  .el-form {
    margin-bottom: 0;
  }
}

.log-item {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.log-user {
  font-weight: bold;
  color: #303133;
  font-size: 14px;
}

.log-button {
  font-size: 12px;
  padding: 4px 8px;
}

.log-content {
  .log-action {
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .log-content-text {
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
    line-height: 1.5;
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
  }

  .related-party {
    display: flex;
    align-items: center;
    margin-top: 8px;
    font-size: 12px;

    .related-label {
      font-weight: 500;
      color: #606266;
      margin-right: 8px;
      min-width: 60px;
    }

    .related-value {
      color: #409eff;
      font-weight: 500;
    }
  }
}

.loading-container {
  padding: 20px;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.log-changes {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;

  .change-item {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .change-field {
      font-weight: bold;
      color: #303133;
      margin-right: 8px;
      min-width: 80px;
    }

    .change-old {
      color: #f56c6c;
      margin-right: 4px;
    }

    .change-arrow {
      color: #909399;
      margin: 0 4px;
    }

    .change-new {
      color: #67c23a;
    }
  }
}

.field-changes {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;

  .changes-title {
    font-weight: bold;
    color: #0369a1;
    margin-bottom: 8px;
    font-size: 13px;
  }

  .field-change-item {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    font-size: 12px;
    padding: 4px 0;

    &:last-child {
      margin-bottom: 0;
    }

    .field-name {
      font-weight: bold;
      color: #303133;
      margin-right: 8px;
      min-width: 80px;
    }

    .field-old {
      color: #f56c6c;
      margin-right: 4px;
      background: #fef2f2;
      padding: 2px 6px;
      border-radius: 3px;
    }

    .change-arrow {
      color: #909399;
      margin: 0 6px;
      font-weight: bold;
    }

    .field-new {
      color: #67c23a;
      background: #f0fdf4;
      padding: 2px 6px;
      border-radius: 3px;
    }
  }
}

.log-json-content {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;

  .json-title {
    font-weight: bold;
    color: #303133;
    margin-bottom: 8px;
    font-size: 13px;
  }

  .json-content {
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 12px;
    margin: 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.5;
    color: #333;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-word;
    max-height: 300px;
    overflow-y: auto;
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-timeline-item__node) {
  background-color: #409eff;
}

:deep(.el-timeline-item__timestamp) {
  color: #909399;
  font-size: 12px;
}

:deep(.el-timeline-item__content) {
  margin-left: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-select) {
  width: 140px;
}

:deep(.el-date-editor) {
  width: 240px;
}
</style>
