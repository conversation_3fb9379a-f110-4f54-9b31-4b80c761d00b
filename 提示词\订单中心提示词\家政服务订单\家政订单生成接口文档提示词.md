提示词请分析以下家政服务订单相关文件：

1. 数据库表结构：`提示词\订单中心提示词\家政服务订单\家政服务订单数据库表结构.sql`
2. 前端页面文件：
   - 主页面：`src\views\OrderCenter\HousekeepingServiceOrder\index.vue`
   - 新增家政服务订单组件：`src\views\OrderCenter\HousekeepingServiceOrder\components\AddHousekeepingServiceOrder.vue`
   - 查看家政服务订单组件：`src\views\OrderCenter\HousekeepingServiceOrder\components\HousekeepingServiceOrderView.vue`
   - 查看操作日志组件：`src\views\OrderCenter\HousekeepingServiceOrder\components\OptLog.vue` --以下文件夹下面的组件：src\views\OrderCenter\HousekeepingServiceOrder\components\forms\
     src\views\OrderCenter\HousekeepingServiceOrder\components\tabs\
     src\views\OrderCenter\HousekeepingServiceOrder\components\modals\

基于这些文件的内容，请为我生成一份完整的家政服务订单API接口文档，不需要新增表结构；文档需要包含： **文档结构要求：**

1. 接口概述和基础信息（基础路径、数据格式、统一响应格式）
2. 接口必须包含一个动词指示该接口的功能，例如page/add/update/delete，不要RESTfull API风格
3. 每个接口的详细说明，包括：
   - 接口地址和请求方式
   - 功能说明
   - 请求参数表格（参数名、类型、必填、说明）
   - 响应字段表格（字段名、类型、说明）
   - 完整的请求示例（包含URL和JSON数据）
   - 完整的返回示例（JSON格式）
4. 数据字典（枚举值说明）
5. 错误码说明

**接口范围：**

- 家政服务订单相关接口（增删改查、分页查询、导出等）
- 操作日志功能、获取套餐、获取服务机构、客户基本信息查询、服务任务列表（完成，指派，取消，编辑，查看凭证过年、查询）、收款信息（查询、新增、修改）、人员变动、收支记录（（取值支付流水表数据）查询、新增、修改、删除）、服务评价（查询、新增、修改、删除）、售后记录(查询、新增、修改、删除)
- 其他从前端代码中识别出的相关接口
- **待结算列表** - 管理待结算订单
- **对账单列表** - 处理对账流程
- **发票管理** - 管理开票信息
- **订单资金列表** - 查看资金流向

### 2. 核心功能流转

#### 2.1 待结算 → 对账单流程

```
待结算订单 → 批量选择 → 生成对账单 → 设置分成比例 → 创建对账单
```

**关键交互点：**

- 支持多条件筛选（时间范围、机构名称、阿姨姓名、订单状态、结算状态）
- 批量选择订单，显示已选择数量
- 生成对账单时设置机构分成比例和平台分成比例
- 自动计算分成金额

#### 2.2 对账单确认流程

```
对账单生成 → 机构确认 → 手工调整(可选) → 确认对账 → 状态更新
```

**关键交互点：**

- 显示原始分成信息（比例和金额）
- 支持启用手工调整分成金额
- 记录调整原因
- 确认后更新对账单状态

#### 2.3 发票管理流程

```
对账单确认 → 维护开票信息 → 设置发票状态 → 填写发票详情 → 保存发票信息
```

**关键交互点：**

- 支持三种发票状态：未开票、已开票、已作废
- 开票信息包括：发票号码、开票日期、发票类型
- 发票类型：增值税普通发票、增值税专用发票、电子发票

#### 2.4 资金流向跟踪

```
订单创建 → 资金托管 → 服务完成 → 结算分配 → 发票开具 → 资金到账
```

**关键交互点：**

- 跟踪订单全生命周期的资金状态
- 显示平台佣金和机构佣金
- 关联对账单号和发票状态

### 3. 详细交互功能

#### 3.1 搜索筛选功能

- **时间范围筛选**：支持创建时间、生成时间、开票日期等
- **状态筛选**：订单状态、结算状态、开票状态、资金状态
- **机构信息筛选**：机构名称、阿姨姓名
- **订单信息筛选**：订单编号、对账单号

#### 3.2 批量操作功能

- **批量选择**：支持全选、部分选择
- **批量生成对账单**：选择多个订单生成对账单
- **批量导出**：支持对账单、发票详情、订单资金列表导出

#### 3.3 弹窗交互功能

- **生成对账单弹窗**：设置分成比例、预览订单信息
- **对账确认弹窗**：查看详情、调整分成、确认对账
- **发票维护弹窗**：设置开票状态、填写发票信息
- **订单详情弹窗**：查看完整订单信息

#### 3.4 数据展示功能

- **统计卡片**：显示待结算、对账单、发票管理、资金列表数量
- **数据表格**：支持排序、分页、行点击等交互
- **状态标签**：不同颜色区分各种状态
- **金额显示**：格式化金额显示，区分不同分成类型

### 4. 技术实现特点

#### 4.1 组件化设计

- 主页面采用Tab组件组织
- 各功能模块独立组件化
- 弹窗组件复用性强

#### 4.2 状态管理

- 统一的状态枚举管理
- 响应式数据绑定
- 表单验证和错误处理

#### 4.3 API接口设计

- RESTful风格接口
- 统一的请求响应格式
- 支持下载和导出功能

### 5. 业务流程完整性

结算中心覆盖了从订单完成到资金结算的完整流程：

1. **订单管理** → 筛选待结算订单
2. **对账处理** → 生成和确认对账单
3. **发票管理** → 维护开票信息
4. **资金跟踪** → 监控资金流向
5. **数据导出** → 支持业务报表需求

这种设计确保了结算业务的完整性和可追溯性，为财务管理和业务分析提供了强有力的支持。

## 请确保文档格式规范、内容完整，便于开发者理解和对接使用。
