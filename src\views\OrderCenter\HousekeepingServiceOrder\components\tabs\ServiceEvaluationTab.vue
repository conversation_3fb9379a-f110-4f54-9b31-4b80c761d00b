<!--
  页面名称：服务评价标签页
  功能描述：展示家政服务订单的服务评价
-->
<template>
  <div class="service-evaluation-tab">
    <div class="section-header">
      <div class="section-title">
        <el-icon><Star /></el-icon>
        服务评价
      </div>
      <div class="header-actions" v-if="evaluationData && evaluationData.overallRating">
        <el-button type="primary" size="small" @click="handleEditEvaluation">
          <el-icon><Edit /></el-icon>
          编辑评价
        </el-button>
      </div>
    </div>

    <div class="evaluation-content">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>
      <div v-else-if="evaluationData && evaluationData.overallRating" class="evaluation-details">
        <div class="evaluation-info">
          <div class="rating-section">
            <div class="rating-display">
              <span class="rating-label">服务评分：</span>
              <el-rate
                v-model="evaluationData.overallRating"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value}分"
              />
            </div>
          </div>

          <div v-if="evaluationTags && evaluationTags.length > 0" class="tags-section">
            <div class="tags-title">评价标签：</div>
            <div class="tags-display">
              <el-tag
                v-for="tag in evaluationTags"
                :key="tag"
                type="success"
                size="small"
                class="evaluation-tag"
              >
                {{ tag }}
              </el-tag>
            </div>
          </div>

          <div v-if="evaluationData.evaluationContent" class="comment-section">
            <div class="comment-title">详细评价：</div>
            <div class="comment-display">
              <div class="quote-mark">"</div>
              <div class="comment-text">{{ evaluationData.evaluationContent }}</div>
            </div>
          </div>

          <div class="evaluation-meta">
            <div class="meta-item">
              <label>评价时间：</label>
              <span>{{ formatDateTime(evaluationData.evaluationTime) }}</span>
            </div>
            <div class="meta-item">
              <label>评价状态：</label>
              <el-tag
                :type="getEvaluationStatusType(evaluationData.evaluationStatus) as any"
                size="small"
              >
                {{ getEvaluationStatusText(evaluationData.evaluationStatus) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="no-evaluation">
        <el-empty description="暂无评价" />
        <div class="evaluation-actions">
          <el-button type="primary" @click="handleAddEvaluation">
            <el-icon><Plus /></el-icon>
            新增评价
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Star, Edit, Plus } from '@element-plus/icons-vue'
import { HousekeepingServiceOrderApi } from '@/api/OrderCenter/HousekeepingServiceOrder'

interface Props {
  orderDetail: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'add-evaluation': []
  'edit-evaluation': [evaluation: any]
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const evaluationData = ref<any>(null)

// 计算属性：解析评价标签
const evaluationTags = computed(() => {
  if (!evaluationData.value?.evaluationTags) return []

  try {
    // 尝试解析JSON字符串
    if (typeof evaluationData.value.evaluationTags === 'string') {
      return JSON.parse(evaluationData.value.evaluationTags)
    }
    // 如果已经是数组，直接返回
    if (Array.isArray(evaluationData.value.evaluationTags)) {
      return evaluationData.value.evaluationTags
    }
    return []
  } catch (error) {
    console.error('解析评价标签失败:', error)
    return []
  }
})

// 获取服务评价
const fetchServiceEvaluation = async () => {
  if (!props.orderDetail?.id) {
    console.log('订单ID不存在，跳过获取服务评价')
    return
  }

  loading.value = true
  try {
    console.log('开始获取服务评价，订单ID:', props.orderDetail.id)
    console.log('订单详情:', props.orderDetail)

    const result = await HousekeepingServiceOrderApi.getServiceEvaluation(props.orderDetail.id)
    console.log('服务评价API响应:', result)

    // 根据新的接口文档处理返回数据
    const resultAny = result as any
    if (resultAny && resultAny.data) {
      // 标准返回格式：{ code: 0, data: { ... } }
      evaluationData.value = resultAny.data
      console.log('使用标准返回格式，评价数据:', evaluationData.value)
    } else if (resultAny && (resultAny.overallRating || resultAny.professionalRating)) {
      // 直接返回评价数据
      evaluationData.value = resultAny
      console.log('使用直接返回格式，评价数据:', evaluationData.value)
    } else {
      // 没有评价数据
      evaluationData.value = null
      console.log('没有评价数据')
    }

    console.log('最终处理后的评价数据:', evaluationData.value)
    console.log('解析后的评价标签:', evaluationTags.value)
  } catch (error) {
    console.error('获取服务评价失败:', error)
    ElMessage.error('获取服务评价失败')
    evaluationData.value = null
  } finally {
    loading.value = false
  }
}

// 监听订单详情变化
watch(
  () => props.orderDetail,
  (newVal) => {
    if (newVal?.id) {
      fetchServiceEvaluation()
    }
  },
  { immediate: true }
)

// 添加评价
const handleAddEvaluation = () => {
  emit('add-evaluation')
}

// 编辑评价
const handleEditEvaluation = () => {
  emit('edit-evaluation', evaluationData.value)
}

// 刷新评价数据
const refreshEvaluation = () => {
  fetchServiceEvaluation()
}

// 格式化日期时间
const formatDateTime = (timestamp: string | number): string => {
  if (!timestamp) return '-'
  try {
    const date = new Date(timestamp)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return timestamp.toString()
  }
}

// 获取评价状态类型
const getEvaluationStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    completed: 'success',
    pending: 'warning',
    processing: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取评价状态文本
const getEvaluationStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    completed: '已完成',
    pending: '待处理',
    processing: '处理中'
  }
  return statusMap[status] || '未知'
}

// 暴露刷新方法给父组件
defineExpose({
  refreshEvaluation
})

// 移除重复的onMounted钩子，避免重复调用
// onMounted(() => {
//   if (props.orderDetail?.id) {
//     fetchServiceEvaluation()
//   }
// })
</script>

<style scoped lang="scss">
.service-evaluation-tab {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .section-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      color: #303133;

      .el-icon {
        margin-right: 8px;
        color: #409eff;
      }
    }
  }

  .evaluation-content {
    .loading-container {
      padding: 20px;
    }

    .evaluation-details {
      .evaluation-info {
        .rating-section {
          margin-bottom: 24px;

          .rating-display {
            display: flex;
            align-items: center;
            gap: 12px;

            .rating-label {
              font-size: 14px;
              font-weight: 500;
              color: #303133;
            }
          }
        }

        .tags-section {
          margin-bottom: 24px;

          .tags-title {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 12px;
          }

          .tags-display {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            .evaluation-tag {
              margin-right: 8px;
            }
          }
        }

        .comment-section {
          margin-bottom: 24px;

          .comment-title {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 12px;
          }

          .comment-display {
            position: relative;
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            border-left: 4px solid #409eff;

            .quote-mark {
              position: absolute;
              top: -8px;
              left: 12px;
              font-size: 24px;
              color: #409eff;
              font-weight: bold;
            }

            .comment-text {
              color: #606266;
              line-height: 1.6;
              font-size: 14px;
              margin-top: 8px;
            }
          }
        }

        .evaluation-meta {
          .meta-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            label {
              min-width: 80px;
              color: #606266;
              font-weight: 500;
              margin-right: 8px;
            }

            span {
              color: #303133;
            }
          }
        }
      }
    }

    .no-evaluation {
      padding: 40px 0;
      text-align: center;

      .evaluation-actions {
        margin-top: 16px;
      }
    }
  }
}
</style>
