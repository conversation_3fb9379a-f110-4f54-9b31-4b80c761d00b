<!--
  页面名称：生成对账单弹窗
  功能描述：生成对账单，支持设置分成比例、查看订单信息等
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="生成对账单"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="generate-reconciliation-dialog"
  >
    <div class="dialog-content">
      <!-- 已选择订单信息 -->
      <div class="info-section">
        <div class="section-title">
          <el-icon><Document /></el-icon>
          <span>已选择订单信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <label>已选择订单：</label>
            <span>{{ selectedOrders.length }}个</span>
          </div>
          <div class="info-item">
            <label>总金额：</label>
            <span class="amount-text">¥{{ formatAmount(totalAmount) }}</span>
          </div>
        </div>
      </div>

      <!-- 分成比例设置 -->
      <div class="info-section">
        <div class="section-title">
          <el-icon><Setting /></el-icon>
          <span>分成比例设置</span>
        </div>
        <el-form :model="splitForm" :rules="splitRules" ref="splitFormRef" label-width="180px">
          <el-form-item label="机构分成比例(%)" prop="agencyRatio" required>
            <el-input
              v-model="splitForm.agencyRatio"
              type="number"
              placeholder="请输入机构分成比例"
              style="width: 200px"
              @input="handleAgencyRatioChange"
            >
              <template #append>%</template>
            </el-input>
          </el-form-item>

          <el-form-item label="机构分成金额" prop="agencyAmount">
            <el-input
              v-model="splitForm.agencyAmount"
              type="number"
              placeholder="机构分成金额"
              style="width: 200px"
              readonly
            >
              <template #prepend>¥</template>
            </el-input>
          </el-form-item>

          <el-form-item label="平台分成比例(%)" prop="platformRatio" required>
            <el-input
              v-model="splitForm.platformRatio"
              type="number"
              placeholder="请输入平台分成比例"
              style="width: 200px"
              @input="handlePlatformRatioChange"
            >
              <template #append>%</template>
            </el-input>
          </el-form-item>

          <el-form-item label="平台分成金额" prop="platformAmount">
            <el-input
              v-model="splitForm.platformAmount"
              type="number"
              placeholder="平台分成金额"
              style="width: 200px"
              readonly
            >
              <template #prepend>¥</template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>

      <!-- 订单列表预览 -->
      <div class="info-section">
        <div class="section-title">
          <el-icon><List /></el-icon>
          <span>订单列表预览</span>
        </div>
        <div class="order-preview">
          <el-table :data="selectedOrders" style="width: 100%" size="small" border>
            <el-table-column prop="orderNo" label="订单编号" width="150" />
            <el-table-column prop="packageName" label="套餐名称" min-width="200" />
            <el-table-column prop="orderAmount" label="订单金额" width="120">
              <template #default="{ row }">
                <span class="amount-text">¥{{ formatAmount(row.orderAmount) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="confirmLoading">
          生成对账单
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Setting, List } from '@element-plus/icons-vue'
import { generateReconciliationBill } from '@/api/settlement/settlement'

/** 弹窗显示状态 */
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

/** 确认加载状态 */
const confirmLoading = ref(false)

/** 分成表单引用 */
const splitFormRef = ref()

/** 分成表单数据 */
const splitForm = reactive({
  agencyRatio: '80',
  agencyAmount: '',
  platformRatio: '20',
  platformAmount: ''
})

/** 分成表单验证规则 */
const splitRules = {
  agencyRatio: [
    { required: true, message: '请输入机构分成比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '比例必须在0-100之间', trigger: 'blur' }
  ],
  platformRatio: [
    { required: true, message: '请输入平台分成比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '比例必须在0-100之间', trigger: 'blur' }
  ]
}

/** 组件属性 */
const props = defineProps<{
  visible: boolean
  selectedOrders: any[]
}>()

/** 组件事件 */
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

/** 计算分成金额 */
const calculateSplitAmounts = () => {
  const agencyRatio = parseFloat(splitForm.agencyRatio) / 100
  const platformRatio = parseFloat(splitForm.platformRatio) / 100

  splitForm.agencyAmount = (totalAmount.value * agencyRatio).toFixed(2)
  splitForm.platformAmount = (totalAmount.value * platformRatio).toFixed(2)
}

/** 总金额 */
const totalAmount = computed(() => {
  return props.selectedOrders.reduce((sum, order) => sum + (order.orderAmount || 0), 0)
})

/** 监听选中订单变化，重新计算分成金额 */
watch(
  () => props.selectedOrders,
  () => {
    calculateSplitAmounts()
  },
  { immediate: true }
)

/** 处理机构分成比例变化 */
const handleAgencyRatioChange = () => {
  const agencyRatio = parseFloat(splitForm.agencyRatio)
  if (!isNaN(agencyRatio) && agencyRatio >= 0 && agencyRatio <= 100) {
    splitForm.platformRatio = (100 - agencyRatio).toString()
    calculateSplitAmounts()
  }
}

/** 处理平台分成比例变化 */
const handlePlatformRatioChange = () => {
  const platformRatio = parseFloat(splitForm.platformRatio)
  if (!isNaN(platformRatio) && platformRatio >= 0 && platformRatio <= 100) {
    splitForm.agencyRatio = (100 - platformRatio).toString()
    calculateSplitAmounts()
  }
}

/** 处理取消 */
const handleCancel = () => {
  dialogVisible.value = false
}

/** 处理确认 */
const handleConfirm = async () => {
  try {
    await splitFormRef.value.validate()

    // 验证分成比例总和是否为100%
    const agencyRatio = parseFloat(splitForm.agencyRatio)
    const platformRatio = parseFloat(splitForm.platformRatio)

    if (Math.abs(agencyRatio + platformRatio - 100) > 0.01) {
      ElMessage.warning('机构分成比例和平台分成比例总和必须为100%')
      return
    }

    confirmLoading.value = true
    const orderIds = props.selectedOrders.map((order) => order.id)

    await generateReconciliationBill(orderIds)
    emit('success')
    dialogVisible.value = false
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('生成对账单失败')
      console.error('生成对账单失败:', error)
    }
  } finally {
    confirmLoading.value = false
  }
}

/** 格式化金额 */
const formatAmount = (amount: number) => {
  return amount?.toFixed(2) || '0.00'
}
</script>

<style scoped lang="scss">
.generate-reconciliation-dialog {
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;

    .el-dialog__title {
      color: white;
      font-weight: 600;
    }

    .el-dialog__headerbtn .el-dialog__close {
      color: white;
    }
  }

  .dialog-content {
    .info-section {
      margin-bottom: 24px;
      padding: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      background: #fafafa;

      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        font-weight: 600;
        color: #303133;

        .el-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }

      .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;

        .info-item {
          display: flex;
          align-items: center;

          label {
            font-weight: 500;
            color: #606266;
            margin-right: 8px;
            min-width: 120px;
          }

          span {
            color: #303133;

            &.amount-text {
              color: #f56c6c;
              font-weight: 600;
            }
          }
        }
      }

      .order-preview {
        .amount-text {
          color: #f56c6c;
          font-weight: 600;
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>
