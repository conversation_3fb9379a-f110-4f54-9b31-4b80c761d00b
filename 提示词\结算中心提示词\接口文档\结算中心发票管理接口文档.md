# 结算中心发票管理接口文档

## 1. 接口概述和基础信息

### 1.1 基础信息
- **基础路径**: `/publicbiz/invoice`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **请求方式**: GET/POST/PUT/DELETE

### 1.2 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-12-01 10:30:00"
}
```

### 1.3 响应状态码说明
- **200**: 请求成功
- **400**: 参数验证错误
- **403**: 权限不足
- **404**: 资源不存在
- **500**: 服务器错误

## 2. 接口详细说明

### 2.1 获取发票管理列表

**接口地址**: `GET /publicbiz/invoice/pageInvoiceList`

**功能说明**: 分页查询发票管理列表，支持多条件筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNo | Integer | 是 | 当前页码，从1开始 |
| pageSize | Integer | 是 | 每页大小，默认10 |
| statementNo | String | 否 | 对账单号，支持模糊查询 |
| agencyName | String | 否 | 机构名称，支持模糊查询 |
| invoiceStatus | String | 否 | 开票状态：not_invoiced-未开票/invoiced-已开票/voided-已作废 |
| startDate | String | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | String | 否 | 结束日期，格式：YYYY-MM-DD |

**请求示例**:
```bash
GET /publicbiz/invoice/pageInvoiceList?pageNo=1&pageSize=10&statementNo=ZD20240816001&invoiceStatus=not_invoiced
```

**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码 |
| message | String | 响应消息 |
| data | Array | 发票列表 |
| total | Integer | 总记录数 |
| pageNo | Integer | 当前页码 |
| pageSize | Integer | 每页大小 |
| data[].id | Long | 发票ID |
| data[].statementId | Long | 关联对账单ID |
| data[].statementNo | String | 对账单号 |
| data[].agencyId | Long | 机构ID |
| data[].agencyName | String | 机构名称 |
| data[].reconciliationAmount | BigDecimal | 对账金额 |
| data[].invoiceNo | String | 发票号码 |
| data[].invoiceDate | String | 开票日期 |
| data[].invoiceType | String | 发票类型 |
| data[].invoiceAmount | BigDecimal | 发票金额 |
| data[].invoiceStatus | String | 开票状态 |
| data[].updateTime | String | 修改时间 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "statementId": 1001,
      "statementNo": "ZD20240816001",
      "agencyId": 1001,
      "agencyName": "阳光家政",
      "reconciliationAmount": 15680.00,
      "invoiceNo": "",
      "invoiceDate": "",
      "invoiceType": "",
      "invoiceAmount": null,
      "invoiceStatus": "not_invoiced",
      "updateTime": "2024-08-16 10:00:00"
    },
    {
      "id": 2,
      "statementId": 1002,
      "statementNo": "ZD20240815001",
      "agencyId": 1002,
      "agencyName": "专业保洁",
      "reconciliationAmount": 8900.00,
      "invoiceNo": "12345678",
      "invoiceDate": "2024-08-16",
      "invoiceType": "增值税专用发票",
      "invoiceAmount": 8900.00,
      "invoiceStatus": "invoiced",
      "updateTime": "2024-08-16 14:30:00"
    }
  ],
  "total": 15,
  "pageNo": 1,
  "pageSize": 10,
  "timestamp": "2024-12-01 10:30:00"
}
```

### 2.2 获取发票详情

**接口地址**: `GET /publicbiz/invoice/getInvoiceDetail`

**功能说明**: 根据对账单号获取发票详细信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| statementNo | String | 是 | 对账单号 |

**请求示例**:
```bash
GET /publicbiz/invoice/getInvoiceDetail?statementNo=ZD20240816001
```

**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码 |
| message | String | 响应消息 |
| data | Object | 发票详细信息 |
| data.id | Long | 发票ID |
| data.statementId | Long | 关联对账单ID |
| data.statementNo | String | 对账单号 |
| data.agencyId | Long | 机构ID |
| data.agencyName | String | 机构名称 |
| data.reconciliationAmount | BigDecimal | 对账金额 |
| data.invoiceNo | String | 发票号码 |
| data.invoiceDate | String | 开票日期 |
| data.invoiceType | String | 发票类型 |
| data.invoiceAmount | BigDecimal | 发票金额 |
| data.taxRate | BigDecimal | 税率(%) |
| data.taxAmount | BigDecimal | 税额 |
| data.invoiceStatus | String | 开票状态 |
| data.invoicingStatus | String | 开票状态 |
| data.invoicingRemark | String | 开票备注 |
| data.operatorId | Long | 操作人ID |
| data.operatorName | String | 操作人姓名 |
| data.remark | String | 备注 |
| data.createTime | String | 创建时间 |
| data.updateTime | String | 更新时间 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "statementId": 1001,
    "statementNo": "ZD20240816001",
    "agencyId": 1001,
    "agencyName": "阳光家政",
    "reconciliationAmount": 15680.00,
    "invoiceNo": "12345678",
    "invoiceDate": "2024-08-16",
    "invoiceType": "增值税专用发票",
    "invoiceAmount": 15680.00,
    "taxRate": 6.00,
    "taxAmount": 940.80,
    "invoiceStatus": "invoiced",
    "invoicingStatus": "invoiced",
    "invoicingRemark": "",
    "operatorId": 1001,
    "operatorName": "张三",
    "remark": "正常开票",
    "createTime": "2024-08-16 10:00:00",
    "updateTime": "2024-08-16 14:30:00"
  },
  "timestamp": "2024-12-01 10:30:00"
}
```

### 2.3 保存开票信息

**接口地址**: `POST /publicbiz/invoice/saveInvoiceInfo`

**功能说明**: 维护开票信息，支持新增和更新

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| statementNo | String | 是 | 对账单号 |
| invoiceStatus | String | 是 | 开票状态：not_invoiced-未开票/invoiced-已开票/voided-已作废 |
| invoiceNo | String | 否 | 发票号码（已开票时必填） |
| invoiceDate | String | 否 | 开票日期（已开票时必填），格式：YYYY-MM-DD |
| invoiceType | String | 否 | 发票类型（已开票时必填）：增值税普通发票/增值税专用发票/电子发票 |
| invoiceAmount | BigDecimal | 否 | 发票金额（已开票时必填） |
| taxRate | BigDecimal | 否 | 税率(%)，默认6.00 |
| remark | String | 否 | 备注说明 |

**请求示例**:
```json
{
  "statementNo": "ZD20240816001",
  "invoiceStatus": "invoiced",
  "invoiceNo": "12345678",
  "invoiceDate": "2024-08-16",
  "invoiceType": "增值税专用发票",
  "invoiceAmount": 15680.00,
  "taxRate": 6.00,
  "remark": "正常开票"
}
```

**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| data.id | Long | 发票ID |
| data.statementNo | String | 对账单号 |
| data.invoiceStatus | String | 开票状态 |
| data.operatorId | Long | 操作人ID |
| data.operatorName | String | 操作人姓名 |
| data.updateTime | String | 更新时间 |

**响应示例**:
```json
{
  "code": 200,
  "message": "开票信息保存成功",
  "data": {
    "id": 1,
    "statementNo": "ZD20240816001",
    "invoiceStatus": "invoiced",
    "operatorId": 1001,
    "operatorName": "张三",
    "updateTime": "2024-08-16 14:30:00"
  },
  "timestamp": "2024-12-01 10:30:00"
}
```

## 3. 数据字典

### 3.1 开票状态（invoiceStatus）
- **not_invoiced**: 未开票
- **invoiced**: 已开票
- **voided**: 已作废

### 3.2 开票状态（invoicingStatus）
- **not_invoiced**: 未开票
- **invoicing**: 开票中
- **invoiced**: 已开票
- **failed**: 开票失败

### 3.3 发票类型（invoiceType）
- **增值税普通发票**: 增值税普通发票
- **增值税专用发票**: 增值税专用发票
- **电子发票**: 电子发票

### 3.3 税率（taxRate）
- **0**: 0%
- **1**: 1%
- **3**: 3%
- **6**: 6%（默认）
- **9**: 9%
- **13**: 13%

## 4. 错误码说明

### 4.1 通用错误码
- **400**: 参数验证错误
- **403**: 权限不足
- **404**: 资源不存在
- **500**: 服务器错误

### 4.2 业务错误码
- **2001**: 对账单号不存在
- **2002**: 发票状态不允许操作
- **2003**: 发票号码已存在
- **2004**: 发票金额不能大于对账金额
- **2005**: 税率设置错误
- **2006**: 导出文件生成失败
- **2007**: 批量操作失败
- **2008**: 发票已作废，不能重复操作

### 4.3 错误响应示例
```json
{
  "code": 2001,
  "message": "对账单号不存在",
  "data": null,
  "timestamp": "2024-12-01 10:30:00"
}
```

## 5. 注意事项

1. **权限控制**: 所有接口都需要进行权限验证，确保用户有相应的操作权限
2. **数据一致性**: 发票信息更新后，相关数据将同步更新
3. **金额精度**: 所有金额字段使用BigDecimal类型，保留2位小数
4. **时间格式**: 所有时间字段使用ISO 8601格式：YYYY-MM-DD HH:mm:ss
5. **分页查询**: 分页查询接口支持排序，默认按创建时间倒序排列
6. **文件导出**: 导出接口返回文件流，需要设置正确的Content-Type
7. **日志记录**: 所有关键操作都需要记录操作日志，包括操作人、操作时间、操作内容等
8. **发票号码**: 发票号码必须唯一，不能重复使用
9. **税率计算**: 税额 = 发票金额 × 税率 / (1 + 税率)
10. **状态流转**: 发票状态只能从未开票→已开票→已作废，不能逆向操作 