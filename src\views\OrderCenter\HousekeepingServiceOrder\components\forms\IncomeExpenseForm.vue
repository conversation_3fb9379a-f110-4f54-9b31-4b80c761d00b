<!--
  页面名称：收支记录表单
  功能描述：新增/编辑家政服务订单的收支记录
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="isEdit ? '编辑收支记录' : '记一笔'"
    direction="rtl"
    size="50%"
    :before-close="handleClose"
  >
    <div class="form-container">
      <div class="form-section">
        <h3 class="section-title">收支信息</h3>

        <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
          <el-form-item label="收支类型" prop="type" required>
            <el-select v-model="form.type" placeholder="请选择收支类型" style="width: 100%">
              <el-option label="额外收入" value="extra_income" />
              <el-option label="赔偿支出" value="compensation_expense" />
            </el-select>
          </el-form-item>

          <el-form-item label="金额" prop="amount" required>
            <el-input
              v-model="form.amount"
              placeholder="请输入金额"
              type="number"
              step="0.01"
              min="0"
            >
              <template #prepend>¥</template>
            </el-input>
          </el-form-item>

          <el-form-item label="日期" prop="date" required>
            <el-date-picker
              v-model="form.date"
              type="date"
              placeholder="请选择日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>

          <el-form-item label="描述" prop="description" required>
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="3"
              placeholder="请输入描述信息"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">保存</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { HousekeepingServiceOrderApi } from '@/api/OrderCenter/HousekeepingServiceOrder'

interface Props {
  visible: boolean
  orderId?: string
  recordData?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderId: '',
  recordData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()
const isEdit = ref(false)

// 表单数据
const form = reactive({
  type: 'extra_income',
  amount: '',
  date: '',
  description: ''
})

// 表单校验规则
const rules: FormRules = {
  type: [{ required: true, message: '请选择类型', trigger: 'change' }],
  amount: [
    { required: true, message: '请输入金额', trigger: 'blur' },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
  ],
  date: [{ required: true, message: '请选择日期', trigger: 'change' }],
  description: [{ required: true, message: '请输入描述', trigger: 'blur' }]
}

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      if (props.recordData) {
        // 编辑模式
        isEdit.value = true
        Object.assign(form, props.recordData)
      } else {
        // 新增模式
        isEdit.value = false
        resetForm()
      }
    }
  }
)

// 监听recordData变化
watch(
  () => props.recordData,
  (newVal) => {
    if (newVal && props.visible) {
      console.log('收支记录编辑回写数据:', newVal)
      Object.assign(form, {
        type: newVal.recordType || newVal.type || '',
        amount: newVal.amount?.toString() || '',
        date: newVal.createTime ? formatDateFromTimestamp(newVal.createTime) : newVal.date || '',
        description: newVal.description || ''
      })
      console.log('回写后的表单数据:', form)
    }
  }
)

// 格式化时间戳为日期
const formatDateFromTimestamp = (timestamp: number): string => {
  if (!timestamp) return ''
  try {
    const date = new Date(timestamp)
    return date.toISOString().split('T')[0]
  } catch (error) {
    return ''
  }
}

const resetForm = () => {
  form.type = 'extra_income'
  form.amount = ''
  form.date = ''
  form.description = ''
  formRef.value?.clearValidate()
}

const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true

    const submitData = {
      orderId: props.orderId,
      recordType: form.type,
      amount: form.amount.toString(),
      description: form.description
    }

    if (isEdit.value && props.recordData?.id) {
      // 编辑模式
      await HousekeepingServiceOrderApi.updateIncomeExpenseRecord({
        recordId: props.recordData.id,
        ...submitData
      })
    } else {
      // 新增模式
      await HousekeepingServiceOrderApi.addIncomeExpenseRecord(submitData)
    }

    ElMessage.success(isEdit.value ? '编辑成功' : '新增成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.form-container {
  padding: 20px;

  .form-section {
    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e4e7ed;
    }
  }
}

.drawer-footer {
  text-align: right;
}
</style>
