# 家政服务订单接口文档

## 1. 订单列表接口

### 1.1 分页查询订单列表

- **接口地址**: `POST /admin-api/publicbiz/domestic-task-order/page`
- **功能说明**: 分页查询家政服务订单列表
- **请求参数**:

| 参数名        | 类型    | 必填 | 说明                         |
| ------------- | ------- | ---- | ---------------------------- |
| page          | Integer | 否   | 当前页码，默认1              |
| size          | Integer | 否   | 每页大小，默认10             |
| orderStatus   | String  | 否   | 订单状态筛选                 |
| paymentStatus | String  | 否   | 支付状态筛选                 |
| serviceType   | String  | 否   | 服务类型筛选                 |
| keyword       | String  | 否   | 关键词搜索（客户姓名、电话） |
| startDate     | String  | 否   | 开始日期（YYYY-MM-DD）       |
| endDate       | String  | 否   | 结束日期（YYYY-MM-DD）       |

**请求示例**:

```json
{
  "page": 1,
  "size": 10,
  "orderStatus": "",
  "paymentStatus": "",
  "serviceType": "",
  "keyword": "",
  "startDate": "",
  "endDate": ""
}
```

**响应字段**:

| 字段名                     | 类型       | 说明         |
| -------------------------- | ---------- | ------------ |
| records                    | Array      | 订单记录列表 |
| records[].id               | Long       | 订单ID       |
| records[].orderNo          | String     | 订单号       |
| records[].customerName     | String     | 客户姓名     |
| records[].customerPhone    | String     | 客户电话     |
| records[].serviceType      | String     | 服务类型     |
| records[].serviceStartDate | String     | 服务开始日期 |
| records[].totalAmount      | BigDecimal | 订单总金额   |
| records[].actualAmount     | BigDecimal | 实际金额     |
| records[].paymentStatus    | String     | 支付状态     |
| records[].orderStatus      | String     | 订单状态     |
| records[].practitionerName | String     | 服务人员姓名 |
| records[].agencyName       | String     | 服务机构名称 |
| records[].createTime       | String     | 创建时间     |
| total                      | Integer    | 总记录数     |
| size                       | Integer    | 每页大小     |
| current                    | Integer    | 当前页码     |
| pages                      | Integer    | 总页数       |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 2027,
        "orderNo": "DS20250822184242194608",
        "customerName": "赵六",
        "customerPhone": "13800138004",
        "serviceType": "保姆",
        "serviceStartDate": "2025-08-22",
        "totalAmount": 599.0,
        "actualAmount": 599.0,
        "paymentStatus": "paid",
        "orderStatus": "pending_payment",
        "practitionerName": "郑阿姨",
        "agencyName": "测试机构A",
        "createTime": "2025-08-22 18:22:45"
      }
    ],
    "total": 27,
    "size": 10,
    "current": 1,
    "pages": 3
  }
}
```

## 2. 订单详情接口

### 2.1 获取订单详情

- **接口地址**: `GET /admin-api/publicbiz/domestic-task-order/get?id={id}`
- **功能说明**: 获取家政服务订单详情
- **路径参数**:

| 参数名 | 类型 | 必填 | 说明   |
| ------ | ---- | ---- | ------ |
| id     | Long | 是   | 订单ID |

**响应字段**:

| 字段名                             | 类型       | 说明          |
| ---------------------------------- | ---------- | ------------- |
| orderInfo                          | Object     | 订单基本信息  |
| orderInfo.id                       | Long       | 订单ID        |
| orderInfo.orderNumber              | String     | 订单号        |
| orderInfo.orderStatus              | String     | 订单状态      |
| orderInfo.paymentStatus            | String     | 支付状态      |
| orderInfo.createTime               | String     | 创建时间      |
| orderInfo.updateTime               | String     | 更新时间      |
| customerInfo                       | Object     | 客户信息      |
| customerInfo.customerName          | String     | 客户姓名      |
| customerInfo.customerPhone         | String     | 客户电话      |
| customerInfo.customerAddress       | String     | 客户地址      |
| customerInfo.customerRemark        | String     | 客户备注      |
| serviceInfo                        | Object     | 服务信息      |
| serviceInfo.serviceType            | String     | 服务类型      |
| serviceInfo.serviceCategoryName    | String     | 服务分类名称  |
| serviceInfo.servicePackageName     | String     | 服务套餐名称  |
| serviceInfo.serviceStartDate       | String     | 服务开始日期  |
| serviceInfo.serviceEndDate         | String     | 服务结束日期  |
| serviceInfo.appointmentTime        | String     | 预约时间      |
| serviceInfo.unitPrice              | BigDecimal | 单价          |
| serviceInfo.totalAmount            | BigDecimal | 总金额        |
| serviceInfo.actualAmount           | BigDecimal | 实际金额      |
| serviceInfo.discountAmount         | BigDecimal | 优惠金额      |
| paymentInfo                        | Object     | 支付信息      |
| paymentInfo.paymentStatus          | String     | 支付状态      |
| paymentInfo.paymentMethod          | String     | 支付方式      |
| paymentInfo.receivedAmount         | BigDecimal | 已收金额      |
| paymentInfo.paymentTime            | String     | 支付时间      |
| paymentInfo.paymentRemark          | String     | 支付备注      |
| practitionerInfo                   | Object     | 服务人员信息  |
| practitionerInfo.practitionerName  | String     | 服务人员姓名  |
| practitionerInfo.practitionerPhone | String     | 服务人员电话  |
| practitionerInfo.practitionerOneid | String     | 服务人员OneID |
| practitionerInfo.experienceYears   | Integer    | 工作经验年限  |
| practitionerInfo.rating            | BigDecimal | 评分          |
| agencyInfo                         | Object     | 服务机构信息  |
| agencyInfo.agencyName              | String     | 服务机构名称  |
| agencyInfo.agencyCode              | String     | 服务机构编码  |
| agencyInfo.agencyType              | String     | 服务机构类型  |
| agencyInfo.cooperationStatus       | String     | 合作状态      |
| agencyInfo.contactPerson           | String     | 联系人        |
| agencyInfo.contactPhone            | String     | 联系电话      |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "orderInfo": {
      "id": 2027,
      "orderNumber": "DS20250822184242194608",
      "orderStatus": "pending_payment",
      "paymentStatus": "paid",
      "createTime": "2025-08-22 18:22:45",
      "updateTime": "2025-08-22 18:42:57"
    },
    "customerInfo": {
      "customerName": "赵六",
      "customerPhone": "13800138004",
      "customerAddress": "222",
      "customerRemark": ""
    },
    "serviceInfo": {
      "serviceType": "保姆",
      "serviceCategoryName": "保姆",
      "servicePackageName": "住家保姆套餐A2",
      "serviceStartDate": "2025-08-22",
      "serviceEndDate": null,
      "appointmentTime": "2025-08-22 00:00:00",
      "unitPrice": 599.0,
      "totalAmount": 599.0,
      "actualAmount": 599.0,
      "discountAmount": 0.0
    },
    "paymentInfo": {
      "paymentStatus": "paid",
      "paymentMethod": "cash",
      "receivedAmount": 599.0,
      "paymentTime": "2025-08-22",
      "paymentRemark": "999"
    },
    "practitionerInfo": {
      "practitionerName": "郑阿姨",
      "practitionerPhone": "19800009090",
      "practitionerOneid": "fa2182d0-768a-11f0-ae0c-00163e1f6ba7",
      "experienceYears": 5,
      "rating": 4.8
    },
    "agencyInfo": {
      "agencyName": "测试机构A",
      "agencyCode": "1001",
      "agencyType": "家政服务",
      "cooperationStatus": "active",
      "contactPerson": "张经理",
      "contactPhone": "13800138001"
    }
  }
}
```

## 3. 订单编辑接口

### 3.1 更新订单信息

- **接口地址**: `POST /admin-api/publicbiz/domestic-task-order/update`
- **功能说明**: 更新家政服务订单信息
- **请求参数**:

| 参数名            | 类型       | 必填 | 说明          |
| ----------------- | ---------- | ---- | ------------- |
| id                | Long       | 是   | 订单ID        |
| customerName      | String     | 是   | 客户姓名      |
| customerPhone     | String     | 是   | 客户电话      |
| serviceAddress    | String     | 是   | 服务地址      |
| serviceStartDate  | LocalDate  | 是   | 服务开始日期  |
| unitPrice         | BigDecimal | 是   | 单价          |
| totalAmount       | BigDecimal | 是   | 总金额        |
| actualAmount      | BigDecimal | 是   | 实际金额      |
| paymentStatus     | String     | 是   | 支付状态      |
| paymentMethod     | String     | 否   | 支付方式      |
| receivedAmount    | BigDecimal | 否   | 已收金额      |
| paymentTime       | LocalDate  | 否   | 支付时间      |
| paymentRemark     | String     | 否   | 支付备注      |
| agencyId          | Long       | 否   | 服务机构ID    |
| agencyName        | String     | 否   | 服务机构名称  |
| practitionerOneid | String     | 否   | 服务人员OneID |
| practitionerName  | String     | 否   | 服务人员姓名  |
| practitionerPhone | String     | 否   | 服务人员电话  |
| remark            | String     | 否   | 备注          |

**请求示例**:

```json
{
  "id": 2027,
  "customerName": "赵六",
  "customerPhone": "13800138004",
  "serviceAddress": "222",
  "serviceStartDate": "2025-08-22",
  "unitPrice": 599.0,
  "totalAmount": 599.0,
  "actualAmount": 599.0,
  "paymentStatus": "paid",
  "paymentMethod": "cash",
  "receivedAmount": 599.0,
  "paymentTime": "2025-08-22",
  "paymentRemark": "999",
  "agencyId": 1001,
  "agencyName": "测试机构A",
  "practitionerOneid": "fa2182d0-768a-11f0-ae0c-00163e1f6ba7",
  "practitionerName": "郑阿姨",
  "practitionerPhone": "19800009090",
  "remark": ""
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": true
}
```

## 4. 支付管理接口

### 4.1 新增收款信息

- **接口地址**: `POST /admin-api/publicbiz/domestic-task-order/payment/add`
- **功能说明**: 新增订单收款信息
- **请求参数**:

| 参数名        | 类型       | 必填 | 说明     |
| ------------- | ---------- | ---- | -------- |
| orderId       | String     | 是   | 订单ID   |
| paymentType   | String     | 是   | 支付类型 |
| paymentAmount | BigDecimal | 是   | 支付金额 |
| paymentTime   | String     | 否   | 支付时间 |
| paymentRemark | String     | 否   | 支付备注 |

**请求示例**:

```json
{
  "orderId": "2027",
  "paymentType": "cash",
  "paymentAmount": 599.0,
  "paymentTime": "2025-08-22",
  "paymentRemark": "999"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "success": true,
    "message": "收款信息添加成功",
    "paymentId": 1234
  }
}
```

### 4.2 修改收款信息

- **接口地址**: `POST /admin-api/publicbiz/domestic-task-order/payment/update`
- **功能说明**: 修改订单收款信息
- **请求参数**:

| 参数名        | 类型       | 必填 | 说明       |
| ------------- | ---------- | ---- | ---------- |
| paymentId     | String     | 是   | 支付记录ID |
| paymentType   | String     | 是   | 支付类型   |
| paymentAmount | BigDecimal | 是   | 支付金额   |
| paymentTime   | String     | 否   | 支付时间   |
| paymentRemark | String     | 否   | 支付备注   |

**请求示例**:

```json
{
  "paymentId": "1234",
  "paymentType": "wechat",
  "paymentAmount": 599.0,
  "paymentTime": "2025-08-22",
  "paymentRemark": "微信支付"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": true
}
```

### 4.3 查询收款信息

- **接口地址**: `POST /admin-api/publicbiz/domestic-task-order/payment/info`
- **功能说明**: 查询订单收款信息
- **请求参数**:

| 参数名  | 类型   | 必填 | 说明   |
| ------- | ------ | ---- | ------ |
| orderId | String | 是   | 订单ID |

**请求示例**:

```json
{
  "orderId": "2027"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1234,
      "paymentNo": "PAY202508221842581234",
      "paymentType": "cash",
      "paymentAmount": 599.0,
      "paymentStatus": "success",
      "paymentTime": "2025-08-22 18:42:58",
      "paymentRemark": "999",
      "operatorName": "系统管理员",
      "createTime": "2025-08-22 18:42:58"
    }
  ]
}
```

## 5. 统计信息接口

### 5.1 获取订单统计信息

- **接口地址**: `GET /admin-api/publicbiz/domestic-task-order/statistics`
- **功能说明**: 获取家政服务订单统计信息
- **请求参数**: 无

**响应字段**:

| 字段名         | 类型       | 说明         |
| -------------- | ---------- | ------------ |
| totalOrders    | Integer    | 总订单数     |
| pendingOrders  | Integer    | 待处理订单数 |
| completionRate | BigDecimal | 完成率       |
| monthlyAmount  | BigDecimal | 本月金额     |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalOrders": 27,
    "pendingOrders": 4,
    "completionRate": 22.22,
    "monthlyAmount": 3393.0
  }
}
```

## 6. 操作日志接口

### 6.1 获取操作日志列表

- **接口地址**: `POST /admin-api/publicbiz/domestic-task-order/log/list`
- **功能说明**: 获取订单操作日志列表
- **请求参数**:

| 参数名    | 类型    | 必填 | 说明                   |
| --------- | ------- | ---- | ---------------------- |
| orderNo   | String  | 是   | 订单号                 |
| logType   | String  | 否   | 日志类型筛选           |
| startDate | String  | 否   | 开始日期（YYYY-MM-DD） |
| endDate   | String  | 否   | 结束日期（YYYY-MM-DD） |
| page      | Integer | 否   | 当前页码，默认1        |
| size      | Integer | 否   | 每页大小，默认10       |

**请求示例**:

```json
{
  "orderNo": "DS20250822184242194608",
  "logType": "订单编辑",
  "startDate": "2025-08-01",
  "endDate": "2025-08-31",
  "page": 1,
  "size": 10
}
```

**响应字段**:

| 字段名                 | 类型    | 说明           |
| ---------------------- | ------- | -------------- |
| records                | Array   | 日志记录列表   |
| records[].id           | Long    | 日志ID         |
| records[].logType      | String  | 日志类型       |
| records[].logTitle     | String  | 日志标题       |
| records[].logContent   | String  | 日志内容(JSON) |
| records[].oldStatus    | String  | 原状态         |
| records[].newStatus    | String  | 新状态         |
| records[].operatorName | String  | 操作人姓名     |
| records[].operatorRole | String  | 操作人角色     |
| records[].createTime   | String  | 创建时间       |
| total                  | Integer | 总记录数       |
| size                   | Integer | 每页大小       |
| current                | Integer | 当前页码       |
| pages                  | Integer | 总页数         |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "logType": "订单编辑",
        "logTitle": "订单信息更新",
        "logContent": "{\"orderId\":2027,\"orderNo\":\"DS20250822184242194608\",\"changes\":{\"customerName\":{\"old\":\"张三\",\"new\":\"赵六\"}},\"updateTime\":\"2025-08-22T18:42:57\",\"operatorId\":1,\"operatorName\":\"系统管理员\"}",
        "oldStatus": "",
        "newStatus": "",
        "operatorName": "系统管理员",
        "operatorRole": "管理员",
        "createTime": "2025-08-22 18:42:57"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

## 7. 状态码说明

| 状态码 | 说明           |
| ------ | -------------- |
| 200    | 操作成功       |
| 400    | 请求参数错误   |
| 401    | 未授权         |
| 403    | 禁止访问       |
| 404    | 资源不存在     |
| 500    | 服务器内部错误 |

## 8. 注意事项

1. 所有金额字段使用 `BigDecimal` 类型，保留2位小数
2. 日期时间字段使用 `LocalDate` 或 `LocalDateTime` 类型
3. 操作日志内容以JSON格式存储，包含详细的变更信息
4. 支付记录支持防重复创建机制
5. 订单状态变更会自动记录操作日志
6. 所有接口都需要进行权限验证
