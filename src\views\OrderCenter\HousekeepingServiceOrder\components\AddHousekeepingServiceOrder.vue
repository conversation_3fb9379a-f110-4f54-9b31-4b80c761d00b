<!--
  页面名称：新增/编辑家政服务订单
  功能描述：新增/编辑家政服务订单，支持表单校验、提交、重置、编辑回显
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="handleUpdateVisible"
    :title="isEdit ? '编辑家政服务订单' : '新建家政服务订单'"
    direction="rtl"
    size="500px"
    :before-close="handleClose"
  >
    <div class="drawer-content">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="order-form">
        <!-- 客户与服务信息模块 -->
        <div class="form-module">
          <div class="module-title">
            <el-icon><User /></el-icon>
            客户与服务信息
          </div>
          <div class="module-content">
            <!-- 关联商机 -->
            <el-form-item label="关联商机" prop="businessOpportunity">
              <el-select
                v-model="form.businessOpportunity"
                placeholder="请选择关联商机 (可选)"
                clearable
                style="width: 100%"
                @change="handleBusinessOpportunityChange"
                :loading="loadingBusinessOpportunities"
              >
                <el-option
                  v-for="item in businessOpportunities"
                  :key="item.id"
                  :label="`${item.name} - ${item.customerName}`"
                  :value="item.id?.toString()"
                />
              </el-select>
              <div class="form-tip">选择关联商机可自动填充部分服务信息</div>
            </el-form-item>

            <!-- 关联线索 -->
            <el-form-item label="关联线索" prop="lead">
              <el-select
                v-model="form.lead"
                placeholder="请选择关联线索 (可选)"
                clearable
                style="width: 100%"
                @change="handleLeadChange"
                :loading="loadingLeads"
              >
                <el-option
                  v-for="item in leads"
                  :key="item.id"
                  :label="`${item.customerName} - ${item.customerPhone}`"
                  :value="item.id?.toString()"
                />
              </el-select>
              <div class="form-tip">选择关联线索可自动填充客户信息</div>
            </el-form-item>

            <!-- 客户姓名 -->
            <el-form-item label="客户姓名" prop="customerName">
              <el-input v-model="form.customerName" placeholder="请输入客户姓名" />
            </el-form-item>

            <!-- 联系电话 -->
            <el-form-item label="联系电话" prop="customerPhone">
              <el-input v-model="form.customerPhone" placeholder="请输入联系电话" />
            </el-form-item>

            <!-- 服务地址 -->
            <el-form-item label="服务地址" prop="serviceAddress">
              <el-input v-model="form.serviceAddress" placeholder="请输入服务地址" />
            </el-form-item>

            <!-- 服务套餐 -->
            <el-form-item label="服务套餐" prop="servicePackage">
              <el-select
                v-model="form.servicePackage"
                placeholder="请选择服务套餐"
                style="width: 100%"
                @change="handleServicePackageChange"
                :loading="loadingServicePackages"
              >
                <el-option
                  v-for="item in servicePackages"
                  :key="item.id"
                  :label="`${item.name} (¥${item.price})`"
                  :value="item.id"
                />
              </el-select>
              <div class="form-tip">选择套餐后会自动填充服务类型和订单金额</div>
            </el-form-item>

            <!-- 服务类型 -->
            <el-form-item label="服务类型" prop="serviceType">
              <el-select
                v-model="form.serviceType"
                placeholder="请选择服务类型"
                style="width: 100%"
                @change="handleServiceTypeChange"
              >
                <el-option
                  v-for="category in serviceCategories"
                  :key="category"
                  :label="getServiceTypeLabel(category)"
                  :value="category"
                />
              </el-select>
              <div class="form-tip">选择服务类型后会自动筛选对应的服务机构</div>
            </el-form-item>

            <!-- 预约上门时间 -->
            <el-form-item label="预约上门时间" prop="appointmentTime">
              <el-date-picker
                v-model="form.appointmentTime"
                type="date"
                placeholder="年-月-日"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>

            <!-- 服务机构 -->
            <el-form-item label="服务机构" prop="serviceAgency">
              <el-select
                v-model="form.serviceAgency"
                placeholder="请选择服务机构 (可选)"
                clearable
                style="width: 100%"
                @change="handleServiceAgencyChange"
                :loading="loadingServiceAgencies"
              >
                <el-option
                  v-for="item in serviceAgencies"
                  :key="item.id"
                  :label="item.agencyName"
                  :value="item.id?.toString()"
                />
              </el-select>
              <div class="form-tip">如果选择服务机构,服务人员将从该机构指派</div>
            </el-form-item>

            <!-- 指派服务人员 -->
            <el-form-item label="指派服务人员" prop="servicePersonnel">
              <el-select
                v-model="form.servicePersonnel"
                placeholder="请选择服务人员"
                style="width: 100%"
                :loading="loadingServicePersonnel"
                :disabled="!form.serviceAgency"
                @change="handleServicePersonnelChange"
              >
                <el-option
                  v-for="(item, index) in servicePersonnel"
                  :key="item.oneid || item.auntOneid || index"
                  :label="`${item.name} - ${item.serviceType} (${item.agencyName})`"
                  :value="item.oneid || item.auntOneid || ''"
                />
              </el-select>
              <div class="form-tip" v-if="servicePersonnel.length === 0 && form.serviceAgency">
                该机构暂无可用服务人员
              </div>
              <div class="form-tip" v-else-if="!form.serviceAgency"> 请先选择服务机构 </div>
            </el-form-item>

            <!-- 订单金额 -->
            <el-form-item label="订单金额" prop="orderAmount">
              <el-input v-model="form.orderAmount" placeholder="请输入订单金额" type="number">
                <template #prefix>¥</template>
              </el-input>
            </el-form-item>

            <!-- 支付状态 -->
            <el-form-item label="支付状态" prop="paymentStatus">
              <el-select
                v-model="form.paymentStatus"
                placeholder="请选择支付状态"
                style="width: 100%"
                @change="handlePaymentStatusChange"
              >
                <el-option label="未支付" value="unpaid" />
                <el-option label="已支付" value="paid" />
                <el-option label="部分支付" value="partial" />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 收款信息模块 - 仅在支付状态为已支付时显示 -->
        <div v-if="form.paymentStatus === 'paid'" class="form-module">
          <div class="module-title">
            <el-icon><CreditCard /></el-icon>
            收款信息
          </div>
          <div class="module-content">
            <!-- 收款方式 -->
            <el-form-item label="收款方式" prop="paymentMethod">
              <el-select
                v-model="form.paymentMethod"
                placeholder="请选择收款方式"
                style="width: 100%"
              >
                <el-option label="现金" value="cash" />
                <el-option label="微信支付" value="wechat" />
                <el-option label="支付宝" value="alipay" />
                <el-option label="银行转账" value="bank_transfer" />
                <el-option label="POS机刷卡" value="pos" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>

            <!-- 收款金额 -->
            <el-form-item label="收款金额" prop="receivedAmount">
              <el-input
                v-model="form.receivedAmount"
                placeholder="请输入收款金额"
                type="number"
                :disabled="true"
              >
                <template #prefix>¥</template>
              </el-input>
              <div class="form-tip">收款金额默认等于订单金额</div>
            </el-form-item>

            <!-- 收款时间 -->
            <el-form-item label="收款时间" prop="paymentTime">
              <el-date-picker
                v-model="form.paymentTime"
                type="date"
                placeholder="请选择收款时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>

            <!-- 收款备注 -->
            <el-form-item label="收款备注" prop="paymentRemark">
              <el-input
                v-model="form.paymentRemark"
                type="textarea"
                placeholder="请输入收款备注"
                :rows="3"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">保存</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Calendar, CreditCard, Phone } from '@element-plus/icons-vue'
import {
  HousekeepingServiceOrderApi,
  getOrderStatusText,
  getPaymentStatusText,
  ORDER_STATUS,
  PAYMENT_STATUS,
  SERVICE_TYPE
} from '@/api/OrderCenter/HousekeepingServiceOrder'
import type {
  BusinessOption,
  LeadOption,
  ServicePackage,
  ServiceAgency,
  ServicePersonnel
} from '@/api/OrderCenter/HousekeepingServiceOrder'

// Props
interface Props {
  visible: boolean
  orderData?: any
}

const props = withDefaults(defineProps<Props>(), {
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref()
const submitting = ref(false)

// 下拉数据
const businessOpportunities = ref<BusinessOption[]>([])
const leads = ref<LeadOption[]>([])
const servicePackages = ref<ServicePackage[]>([])
const serviceAgencies = ref<ServiceAgency[]>([])
const servicePersonnel = ref<ServicePersonnel[]>([])
const serviceCategories = ref<string[]>([])

// 加载状态
const loadingBusinessOpportunities = ref(false)
const loadingLeads = ref(false)
const loadingServicePackages = ref(false)
const loadingServiceAgencies = ref(false)
const loadingServicePersonnel = ref(false)

// 表单数据
const form = reactive({
  businessOpportunity: '',
  lead: '',
  customerName: '',
  customerPhone: '',
  serviceAddress: '',
  servicePackage: '',
  serviceType: '',
  appointmentTime: '',
  servicePersonnel: '',
  serviceAgency: '',
  orderAmount: '',
  serviceDuration: '',
  serviceUnit: '',
  paymentStatus: 'unpaid',
  // 收款信息字段
  paymentMethod: '',
  receivedAmount: '',
  paymentTime: '',
  paymentRemark: '',
  // 添加服务机构和服务人员的下沉字段
  agencyId: '',
  agencyCode: '',
  agencyName: '',
  practitionerOneid: '',
  practitionerName: '',
  practitionerPhone: '',
  // 添加缺失的字段
  unitPrice: '',
  totalAmount: '',
  actualAmount: '',
  serviceStartDate: '',
  serviceEndDate: '',
  serviceAmount: '',
  serviceFrequency: '',
  serviceDescription: '',
  remark: ''
})

// 表单校验规则
const rules = {
  customerName: [{ required: true, message: '请输入客户姓名', trigger: 'blur' }],
  customerPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  serviceAddress: [{ required: true, message: '请输入服务地址', trigger: 'blur' }],
  servicePackage: [{ required: true, message: '请选择服务套餐', trigger: 'change' }],
  serviceType: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
  appointmentTime: [{ required: true, message: '请选择预约时间', trigger: 'change' }],
  servicePersonnel: [{ required: true, message: '请输入服务人员', trigger: 'blur' }],
  orderAmount: [
    { required: true, message: '请输入订单金额', trigger: 'blur' },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
  ],
  paymentStatus: [{ required: true, message: '请选择支付状态', trigger: 'change' }]
}

// 计算属性
const isEdit = computed(() => !!props.orderData)

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal && props.orderData) {
      // 编辑模式，回显数据
      Object.assign(form, props.orderData)
    } else if (newVal && !props.orderData) {
      // 新增模式，重置表单
      resetForm()
    }
  }
)

// 监听编辑数据变化
watch(
  () => props.orderData,
  async (newVal) => {
    if (newVal && isEdit.value) {
      console.log('检测到编辑模式，开始回写数据:', newVal)
      // 如果有订单ID，先获取最新详情
      if (newVal.id) {
        try {
          console.log('获取订单详情进行回写，订单ID:', newVal.id)
          const detailResult = await HousekeepingServiceOrderApi.getOrderDetail(newVal.id)
          console.log('订单详情回写数据:', detailResult)

          // 时间戳转换函数
          const formatTimestamp = (timestamp: number | null): string => {
            if (!timestamp) return ''
            try {
              const date = new Date(timestamp)
              return date.toISOString().split('T')[0]
            } catch (error) {
              return ''
            }
          }

          // 回写表单数据
          Object.assign(form, {
            // 商机和线索信息 - 修复字段绑定问题
            businessOpportunity: detailResult.orderInfo?.opportunityId?.toString() || '',
            lead: detailResult.orderInfo?.leadId?.toString() || '',

            // 客户信息
            customerName: detailResult.customerInfo?.customerName || '',
            customerPhone: detailResult.customerInfo?.customerPhone || '',
            serviceAddress: detailResult.customerInfo?.serviceAddress || '',

            // 服务信息
            serviceType: detailResult.serviceInfo?.serviceType || '',
            servicePackage: detailResult.serviceInfo?.servicePackage || '',
            serviceStartDate: detailResult.serviceInfo?.serviceStartDate || '',
            serviceEndDate: detailResult.serviceInfo?.serviceEndDate || '',
            serviceDuration: detailResult.serviceInfo?.serviceDuration || '',
            serviceFrequency: detailResult.serviceInfo?.serviceFrequency || '',
            appointmentTime: formatTimestamp(detailResult.serviceInfo?.serviceStartDate) || '', // 修复预约上门时间回写
            serviceDescription: detailResult.serviceInfo?.serviceDescription || '',

            // 金额信息 - 修复字段下沉问题
            unitPrice: detailResult.serviceInfo?.serviceAmount?.toString() || '',
            totalAmount: detailResult.serviceInfo?.serviceAmount?.toString() || '',
            actualAmount:
              detailResult.paymentInfo?.actualAmount?.toString() ||
              detailResult.paymentInfo?.totalAmount?.toString() ||
              '',
            discountAmount: detailResult.paymentInfo?.discountAmount?.toString() || '0',
            orderAmount: detailResult.serviceInfo?.serviceAmount?.toString() || '',

            // 服务人员信息 - 修复字段下沉问题
            practitionerOneid: detailResult.orderInfo?.practitionerOneid?.toString() || '',
            practitionerName: detailResult.practitionerInfo?.practitionerName || '',
            practitionerPhone: detailResult.practitionerInfo?.practitionerPhone || '',
            servicePersonnel: detailResult.orderInfo?.practitionerOneid?.toString() || '', // 设置服务人员选择框的值

            // 机构信息 - 修复字段下沉问题
            agencyId: detailResult.agencyInfo?.agencyId?.toString() || '',
            agencyName: detailResult.agencyInfo?.agencyName || '',
            agencyCode: detailResult.agencyInfo?.agencyCode || '',
            serviceAgency: detailResult.agencyInfo?.agencyId?.toString() || '', // 设置服务机构选择框的值

            // 支付信息 - 修复支付状态和支付方式回写
            paymentStatus: detailResult.orderInfo?.paymentStatus || 'unpaid',
            paymentMethod: detailResult.paymentInfo?.paymentMethod || '',
            receivedAmount:
              detailResult.paymentInfo?.receivedAmount?.toString() ||
              detailResult.paymentInfo?.paidAmount?.toString() ||
              detailResult.paymentInfo?.totalAmount?.toString() ||
              detailResult.serviceInfo?.serviceAmount?.toString() ||
              '', // 修复receivedAmount回写
            paymentTime: formatTimestamp(detailResult.paymentInfo?.paymentTime) || '',
            paymentRemark: detailResult.paymentInfo?.paymentRemark || '',

            // 备注信息
            remark: detailResult.customerInfo?.customerRemark || ''
          })

          // 加载相关的下拉数据
          await Promise.all([loadBusinessOpportunities(), loadLeads(), loadAllServiceAgencies()])

          // 加载服务套餐数据
          try {
            loadingServicePackages.value = true
            const packageData = await HousekeepingServiceOrderApi.getServicePackageList()
            if (packageData.data && Array.isArray(packageData.data)) {
              servicePackages.value = packageData.data
            } else if (packageData.packages) {
              servicePackages.value = packageData.packages
            } else {
              servicePackages.value = []
            }
          } catch (error) {
            console.error('加载服务套餐数据失败:', error)
          } finally {
            loadingServicePackages.value = false
          }

          // 如果有服务机构，加载对应的服务人员
          if (form.serviceAgency) {
            await loadServicePersonnel()
          }

          console.log('编辑模式数据回写完成')
        } catch (error) {
          console.error('获取订单详情失败:', error)
          ElMessage.error('获取订单详情失败，请重试')
        }
      } else {
        // 如果没有订单ID，直接使用传入的数据
        console.log('使用传入的订单数据进行回写:', newVal)
        Object.assign(form, newVal)
      }
    }
  },
  { immediate: true }
)

// 组件挂载时初始化下拉数据
onMounted(() => {
  // 只在组件首次挂载时初始化数据，避免重复请求
  if (businessOpportunities.value.length === 0 && leads.value.length === 0) {
    initDropdownData()
  }
})

// 方法定义

// 初始化下拉数据
const initDropdownData = async () => {
  try {
    console.log('开始初始化下拉数据...')

    // 设置加载状态
    loadingBusinessOpportunities.value = true
    loadingLeads.value = true
    loadingServicePackages.value = true
    loadingServiceAgencies.value = true

    // 获取商机和线索数据 - 使用高校实践订单的接口
    const dropdownData = await HousekeepingServiceOrderApi.getDropdownData({
      orderType: 'domestic',
      businessLine: '家政服务'
    })
    businessOpportunities.value = dropdownData.businessOptions || []
    leads.value = dropdownData.leadOptions || []
    console.log('商机和线索数据加载完成')

    // 获取服务套餐数据
    const packageData = await HousekeepingServiceOrderApi.getServicePackageList()
    // 处理分页数据格式
    if (packageData.data && Array.isArray(packageData.data)) {
      servicePackages.value = packageData.data
    } else if (packageData.packages) {
      servicePackages.value = packageData.packages
    } else {
      servicePackages.value = []
    }

    // 提取服务分类（去重）
    const categories = new Set<string>()
    servicePackages.value.forEach((pkg) => {
      if (pkg.category) {
        categories.add(pkg.category)
      }
    })
    serviceCategories.value = Array.from(categories)

    console.log('服务套餐数据加载完成')
    console.log('提取的服务分类:', serviceCategories.value)

    // 获取服务机构数据
    const agencyData = await HousekeepingServiceOrderApi.getServiceAgencyList()
    // 处理分页数据格式
    if (agencyData.data && Array.isArray(agencyData.data)) {
      serviceAgencies.value = agencyData.data
    } else if (agencyData.agencies) {
      serviceAgencies.value = agencyData.agencies
    } else {
      serviceAgencies.value = []
    }
    console.log('服务机构数据加载完成')

    console.log('初始化数据完成:', {
      businessOpportunities: businessOpportunities.value.length,
      leads: leads.value.length,
      servicePackages: servicePackages.value.length,
      serviceAgencies: serviceAgencies.value.length
    })
  } catch (error) {
    console.error('初始化下拉数据失败:', error)
    ElMessage.error('初始化数据失败')
  } finally {
    // 清除加载状态
    loadingBusinessOpportunities.value = false
    loadingLeads.value = false
    loadingServicePackages.value = false
    loadingServiceAgencies.value = false
  }
}

// 单独加载商机数据
const loadBusinessOpportunities = async () => {
  try {
    loadingBusinessOpportunities.value = true
    const dropdownData = await HousekeepingServiceOrderApi.getDropdownData({
      orderType: 'domestic',
      businessLine: '家政服务'
    })
    businessOpportunities.value = dropdownData.businessOptions || []
    console.log('商机数据加载完成:', businessOpportunities.value.length)
  } catch (error) {
    console.error('加载商机数据失败:', error)
  } finally {
    loadingBusinessOpportunities.value = false
  }
}

// 单独加载线索数据
const loadLeads = async () => {
  try {
    loadingLeads.value = true
    const dropdownData = await HousekeepingServiceOrderApi.getDropdownData({
      orderType: 'domestic',
      businessLine: '家政服务'
    })
    leads.value = dropdownData.leadOptions || []
    console.log('线索数据加载完成:', leads.value.length)
  } catch (error) {
    console.error('加载线索数据失败:', error)
  } finally {
    loadingLeads.value = false
  }
}

// 单独加载服务人员数据
const loadServicePersonnel = async () => {
  try {
    loadingServicePersonnel.value = true
    const personnelData = await HousekeepingServiceOrderApi.getServicePersonnelList({
      agencyId: form.serviceAgency ? parseInt(form.serviceAgency) : undefined
    })

    if (personnelData.data && Array.isArray(personnelData.data)) {
      servicePersonnel.value = personnelData.data
    } else if (personnelData.practitioners) {
      servicePersonnel.value = personnelData.practitioners
    } else {
      servicePersonnel.value = []
    }
    console.log('服务人员数据加载完成:', servicePersonnel.value.length)
  } catch (error) {
    console.error('加载服务人员数据失败:', error)
  } finally {
    loadingServicePersonnel.value = false
  }
}

// 商机选择变化
const handleBusinessOpportunityChange = (value: string) => {
  if (value) {
    const selectedOpportunity = businessOpportunities.value.find(
      (item) => item.id?.toString() === value
    )
    if (selectedOpportunity) {
      // 自动填充客户信息
      form.customerName = selectedOpportunity.customerName
      // 可以根据需要填充其他信息
    }
  }
}

// 线索选择变化
const handleLeadChange = (value: string) => {
  if (value) {
    const selectedLead = leads.value.find((item) => item.id?.toString() === value)
    if (selectedLead) {
      // 自动填充客户信息
      form.customerName = selectedLead.customerName
      form.customerPhone = selectedLead.customerPhone
    }
  }
}

// 服务套餐选择变化
const handleServicePackageChange = async (value: string) => {
  console.log('服务套餐选择变化:', value)
  if (value) {
    const selectedPackage = servicePackages.value.find((item) => item.id?.toString() === value)
    console.log('选中的服务套餐:', selectedPackage)
    if (selectedPackage) {
      // 根据套餐自动填充服务类型和金额
      form.serviceType = selectedPackage.category || ''
      form.orderAmount = selectedPackage.price?.toString() || '0'
      form.serviceDuration = selectedPackage.serviceDuration || ''
      form.serviceUnit = selectedPackage.unit || ''

      // 清空服务机构和服务人员选择
      form.serviceAgency = ''
      form.servicePersonnel = ''
      servicePersonnel.value = []

      // 根据服务类型筛选服务机构
      if (form.serviceType) {
        await filterServiceAgenciesByServiceType(form.serviceType)
      }

      console.log('服务套餐联动完成:', {
        serviceType: form.serviceType,
        orderAmount: form.orderAmount,
        serviceDuration: form.serviceDuration
      })
    }
  } else {
    form.serviceType = ''
    form.orderAmount = '0'
    form.serviceDuration = ''
    form.serviceUnit = ''
    form.serviceAgency = ''
    form.servicePersonnel = ''
    servicePersonnel.value = []
    // 清空服务机构筛选，加载所有机构
    await loadAllServiceAgencies()
  }
}

// 根据服务类型筛选服务机构
const filterServiceAgenciesByServiceType = async (serviceType: string) => {
  if (!serviceType) {
    await loadAllServiceAgencies()
    return
  }

  try {
    loadingServiceAgencies.value = true
    // 根据服务类型获取对应的服务机构
    const agencyData = await HousekeepingServiceOrderApi.getServiceAgencyList({
      serviceType: serviceType
    })

    // 处理分页数据格式
    if (agencyData.data && Array.isArray(agencyData.data)) {
      serviceAgencies.value = agencyData.data
    } else if (agencyData.agencies) {
      serviceAgencies.value = agencyData.agencies
    } else {
      serviceAgencies.value = []
    }

    console.log(`根据服务类型"${serviceType}"筛选的服务机构:`, serviceAgencies.value)
  } catch (error) {
    console.error('筛选服务机构失败:', error)
    // 如果筛选失败，加载所有机构
    await loadAllServiceAgencies()
  } finally {
    loadingServiceAgencies.value = false
  }
}

// 单独加载服务机构数据
const loadServiceAgencies = async () => {
  try {
    loadingServiceAgencies.value = true
    const agencyData = await HousekeepingServiceOrderApi.getServiceAgencyList()

    // 处理分页数据格式
    if (agencyData.data && Array.isArray(agencyData.data)) {
      serviceAgencies.value = agencyData.data
    } else if (agencyData.agencies) {
      serviceAgencies.value = agencyData.agencies
    } else {
      serviceAgencies.value = []
    }

    console.log('服务机构数据加载完成:', serviceAgencies.value.length)
  } catch (error) {
    console.error('加载服务机构数据失败:', error)
  } finally {
    loadingServiceAgencies.value = false
  }
}

// 加载所有服务机构
const loadAllServiceAgencies = async () => {
  try {
    loadingServiceAgencies.value = true
    const agencyData = await HousekeepingServiceOrderApi.getServiceAgencyList()

    // 处理分页数据格式
    if (agencyData.data && Array.isArray(agencyData.data)) {
      serviceAgencies.value = agencyData.data
    } else if (agencyData.agencies) {
      serviceAgencies.value = agencyData.agencies
    } else {
      serviceAgencies.value = []
    }

    console.log('加载所有服务机构:', serviceAgencies.value)
  } catch (error) {
    console.error('加载服务机构失败:', error)
    ElMessage.error('加载服务机构失败')
  } finally {
    loadingServiceAgencies.value = false
  }
}

// 服务类型标签转换
const getServiceTypeLabel = (category: string): string => {
  const labelMap: Record<string, string> = {
    maternity: '月嫂服务',
    deep_cleaning: '深度保洁',
    hourly: '小时工',
    nanny: '育儿嫂服务',
    range_hood_cleaning: '油烟机清洗',
    daily_cleaning: '日常保洁',
    glass_cleaning: '玻璃清洗',
    housekeeping: '家政服务',
    cleaning: '保洁服务',
    cooking: '烹饪服务',
    elderly_care: '老人护理',
    child_care: '儿童护理'
  }
  return labelMap[category] || category
}

// 服务类型变化处理
const handleServiceTypeChange = async (value: string) => {
  console.log('服务类型变化:', value)
  if (value) {
    // 清空服务机构和服务人员选择
    form.serviceAgency = ''
    form.servicePersonnel = ''
    servicePersonnel.value = []

    // 根据服务类型筛选服务机构
    await filterServiceAgenciesByServiceType(value)
  } else {
    // 清空服务机构和服务人员选择
    form.serviceAgency = ''
    form.servicePersonnel = ''
    servicePersonnel.value = []

    // 加载所有服务机构
    await loadAllServiceAgencies()
  }
}

// 服务机构选择变化
const handleServiceAgencyChange = async (value: string) => {
  console.log('服务机构选择变化:', value)
  form.servicePersonnel = '' // 清空服务人员选择
  servicePersonnel.value = [] // 清空服务人员列表

  if (value) {
    try {
      loadingServicePersonnel.value = true
      // 根据选择的机构获取服务人员列表
      console.log('获取服务人员参数:', { agencyId: parseInt(value) })
      const personnelData = await HousekeepingServiceOrderApi.getServicePersonnelList({
        agencyId: parseInt(value)
      })
      console.log('服务人员接口返回数据:', personnelData)
      // 处理分页数据格式
      if (personnelData.data && Array.isArray(personnelData.data)) {
        servicePersonnel.value = personnelData.data
        console.log('使用 personnelData.data 格式')
      } else if (personnelData.practitioners) {
        servicePersonnel.value = personnelData.practitioners
        console.log('使用 personnelData.practitioners 格式')
      } else {
        servicePersonnel.value = []
        console.log('使用空数组格式')
      }
      console.log('处理后的服务人员数据:', servicePersonnel.value)
      console.log('服务人员数量:', servicePersonnel.value.length)

      // 调试：打印每个服务人员的关键字段
      servicePersonnel.value.forEach((person, index) => {
        console.log(`服务人员${index + 1}:`, {
          auntOneid: person.auntOneid,
          oneid: person.oneid,
          id: person.id,
          name: person.name,
          serviceType: person.serviceType,
          agencyName: person.agencyName
        })
      })

      // 验证服务人员数据完整性
      const validPersonnel = servicePersonnel.value.filter(
        (person) => (person.oneid || person.auntOneid) && person.name
      )
      console.log('有效服务人员数量:', validPersonnel.length)

      if (validPersonnel.length === 0 && servicePersonnel.value.length > 0) {
        console.warn('服务人员数据格式可能有问题')
        ElMessage.warning('服务人员数据格式异常，请检查接口返回数据')
      }

      // 下沉服务机构信息到表单
      const selectedAgency = serviceAgencies.value.find((agency) => agency.id?.toString() === value)
      if (selectedAgency) {
        console.log('选中的服务机构:', selectedAgency)
        form.agencyId = selectedAgency.id?.toString() || ''
        form.agencyCode = selectedAgency.id?.toString() || '' // 使用ID作为编码
        form.agencyName = selectedAgency.agencyName || ''
        console.log('服务机构信息已下沉到表单:', {
          agencyId: form.agencyId,
          agencyCode: form.agencyCode,
          agencyName: form.agencyName
        })
      }
    } catch (error) {
      console.error('获取服务人员列表失败:', error)
      ElMessage.error('获取服务人员列表失败')
    } finally {
      loadingServicePersonnel.value = false
    }
  }
}

// 服务人员选择变化处理
const handleServicePersonnelChange = (value: string) => {
  console.log('服务人员选择变化:', value)
  if (value) {
    const selectedPersonnel = servicePersonnel.value.find(
      (person) => person.oneid === value || person.auntOneid === value
    )
    if (selectedPersonnel) {
      console.log('选中的服务人员:', selectedPersonnel)

      // 下沉服务人员信息到表单
      form.practitionerOneid = selectedPersonnel.oneid || selectedPersonnel.auntOneid || ''
      form.practitionerName = selectedPersonnel.name || ''
      form.practitionerPhone = selectedPersonnel.phone || ''

      console.log('服务人员信息已下沉到表单:', {
        practitionerOneid: form.practitionerOneid,
        practitionerName: form.practitionerName,
        practitionerPhone: form.practitionerPhone
      })

      ElMessage.success(`已选择服务人员: ${selectedPersonnel.name}`)
    }
  } else {
    console.log('清空服务人员选择')
    // 清空服务人员下沉信息
    form.practitionerOneid = ''
    form.practitionerName = ''
    form.practitionerPhone = ''
  }
}

// 支付状态变化处理
const handlePaymentStatusChange = (value: string) => {
  if (value === 'paid') {
    // 当选择已支付时，自动设置收款金额为订单金额
    form.receivedAmount = form.orderAmount
    // 设置默认收款时间为当前时间
    form.paymentTime = new Date().toISOString().slice(0, 10)
  } else {
    // 当选择其他支付状态时，清空收款信息
    form.paymentMethod = ''
    form.receivedAmount = ''
    form.paymentTime = ''
    form.paymentRemark = ''
  }
}

// 支付方式映射
const getPaymentMethodText = (method: string): string => {
  const methodMap: Record<string, string> = {
    cash: '现金',
    wechat: '微信支付',
    alipay: '支付宝',
    bank_transfer: '银行转账',
    pos: 'POS机刷卡',
    other: '其他',
    待支付: '待支付'
  }
  return methodMap[method] || method
}

const resetForm = () => {
  Object.assign(form, {
    businessOpportunity: '',
    lead: '',
    customerName: '',
    customerPhone: '',
    serviceAddress: '',
    servicePackage: '',
    serviceType: '',
    appointmentTime: '',
    servicePersonnel: '',
    serviceAgency: '',
    orderAmount: '',
    serviceDuration: '',
    serviceUnit: '',
    paymentStatus: 'unpaid',
    // 收款信息字段
    paymentMethod: '',
    receivedAmount: '',
    paymentTime: '',
    paymentRemark: ''
  })
  formRef.value?.clearValidate()
}

const handleClose = () => {
  emit('update:visible', false)
}

const handleUpdateVisible = (newVal: boolean) => {
  emit('update:visible', newVal)
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true

    const submitData = {
      businessOpportunity: form.businessOpportunity,
      lead: form.lead,
      customerName: form.customerName,
      customerPhone: form.customerPhone,
      serviceAddress: form.serviceAddress,
      servicePackage: form.servicePackage,
      serviceType: form.serviceType,
      serviceStartDate: form.appointmentTime,
      unitPrice: parseFloat(form.orderAmount),
      totalAmount: parseFloat(form.orderAmount),
      actualAmount: parseFloat(form.orderAmount),
      remark: '',
      // 添加支付相关信息
      paymentStatus: form.paymentStatus,
      paymentMethod: form.paymentMethod,
      receivedAmount: form.receivedAmount
        ? parseFloat(form.receivedAmount)
        : parseFloat(form.orderAmount), // 修复receivedAmount保存赋值
      paymentTime: form.paymentTime,
      paymentRemark: form.paymentRemark,
      // 添加服务机构和服务人员下沉信息
      agencyId: form.agencyId ? parseInt(form.agencyId) : undefined,
      agencyCode: form.agencyCode,
      agencyName: form.agencyName,
      practitionerOneid: form.practitionerOneid,
      practitionerName: form.practitionerName,
      practitionerPhone: form.practitionerPhone,
      // 添加后端需要的额外字段
      customerAddress: form.serviceAddress, // 客户地址使用服务地址
      customerOneid: '', // 客户OneID，设置默认空字符串
      serviceCategoryId: 1, // 服务分类ID，设置默认值
      serviceCategoryName: form.serviceType, // 服务分类名称使用服务类型
      servicePackageId: form.servicePackage ? parseInt(form.servicePackage) : undefined, // 服务套餐ID
      servicePackageName: (() => {
        if (form.servicePackage) {
          const selectedPackage = servicePackages.value.find(
            (pkg) => pkg.id?.toString() === form.servicePackage
          )
          return selectedPackage?.name || ''
        }
        return ''
      })(), // 服务套餐名称，从套餐数据中获取
      servicePackagePrice: parseFloat(form.orderAmount), // 服务套餐价格
      servicePackageUnit: '次', // 价格单位，设置默认值
      servicePackageType: 'count-card', // 套餐类型，设置默认值
      discountAmount: 0 // 优惠金额，设置默认值
    }

    if (isEdit.value) {
      // 编辑模式 - 根据最新接口文档更新
      const updateData = {
        id: props.orderData.id,
        customerName: form.customerName,
        customerPhone: form.customerPhone,
        serviceAddress: form.serviceAddress,
        serviceStartDate: form.appointmentTime,
        unitPrice: parseFloat(form.orderAmount),
        totalAmount: parseFloat(form.orderAmount),
        actualAmount: parseFloat(form.orderAmount),
        paymentStatus: form.paymentStatus,
        paymentMethod: form.paymentMethod || undefined,
        receivedAmount: form.receivedAmount
          ? parseFloat(form.receivedAmount)
          : parseFloat(form.orderAmount), // 修复receivedAmount保存赋值
        paymentTime: form.paymentTime || undefined,
        paymentRemark: form.paymentRemark || undefined,
        agencyId: form.agencyId ? Number(form.agencyId) : undefined,
        agencyName: form.agencyName || undefined,
        practitionerOneid: form.practitionerOneid || undefined,
        practitionerName: form.practitionerName || undefined,
        practitionerPhone: form.practitionerPhone || undefined,
        remark: ''
      }

      await HousekeepingServiceOrderApi.updateOrder(updateData)

      // 如果支付状态为已支付且有收款信息，则更新收款信息
      if (form.paymentStatus === 'paid' && form.receivedAmount && form.paymentMethod) {
        try {
          // 先尝试获取现有支付信息
          const paymentInfo = await HousekeepingServiceOrderApi.getPaymentInfo(props.orderData.id)
          if (paymentInfo && Array.isArray(paymentInfo) && paymentInfo.length > 0) {
            // 如果存在支付信息，则更新第一条记录
            await HousekeepingServiceOrderApi.updatePaymentInfo({
              paymentId: paymentInfo[0].id,
              paymentType: form.paymentMethod,
              paymentAmount: parseFloat(form.receivedAmount),
              paymentTime: form.paymentTime,
              paymentRemark: form.paymentRemark
            })
          } else {
            // 如果不存在支付信息，则新增
            await HousekeepingServiceOrderApi.addPaymentInfo({
              orderId: props.orderData.id,
              paymentType: form.paymentMethod,
              paymentAmount: parseFloat(form.receivedAmount),
              paymentTime: form.paymentTime,
              paymentRemark: form.paymentRemark
            })
          }
        } catch (paymentError) {
          console.error('更新收款信息失败:', paymentError)
          ElMessage.warning('订单更新成功，但收款信息更新失败')
        }
      }
    } else {
      // 新增模式
      const result = await HousekeepingServiceOrderApi.addOrder(submitData)

      // 如果支付状态为已支付，则添加收款信息
      if (form.paymentStatus === 'paid' && result.orderId) {
        try {
          await HousekeepingServiceOrderApi.addPaymentInfo({
            orderId: result.orderId,
            paymentType: form.paymentMethod,
            paymentAmount: parseFloat(form.receivedAmount),
            paymentTime: form.paymentTime,
            paymentRemark: form.paymentRemark
          })
        } catch (paymentError) {
          console.error('添加收款信息失败:', paymentError)
          ElMessage.warning('订单创建成功，但收款信息添加失败')
        }
      }
    }

    ElMessage.success(isEdit.value ? '编辑成功' : '新增成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('表单提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped lang="scss">
.drawer-content {
  padding: 10px;
  height: calc(100vh - 120px);
  overflow-y: auto;

  .order-form {
    .form-module {
      background-color: #f8f9fa;
      border-radius: 8px;
      padding: 10px;
      margin-bottom: 10px;
      overflow: visible;

      .module-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e4e7ed;

        .el-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }

      .module-content {
        overflow: visible;

        .el-form-item {
          margin-bottom: 15px;

          .el-form-item__label {
            font-weight: 500;
            color: #333;
          }

          .el-input__wrapper,
          .el-select .el-input__wrapper {
            border-radius: 4px;
            box-shadow: 0 0 0 1px #dcdfe6 inset;

            &:hover {
              box-shadow: 0 0 0 1px #c0c4cc inset;
            }

            &.is-focus {
              box-shadow: 0 0 0 1px #409eff inset;
            }
          }

          .el-date-editor {
            .el-input__wrapper {
              border-radius: 4px;
            }
          }
        }

        .form-tip {
          font-size: 12px;
          color: #909399;
          margin-top: 2px;
          line-height: 1.4;
        }
      }
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
  background-color: #fff;

  .el-button {
    border-radius: 4px;
    font-weight: 500;
  }
}
</style>
