<!--
  页面名称：换人申请工单详情
  功能描述：展示换人申请工单详细信息，包括基本信息、关联方信息、服务任务管理等
-->
<template>
  <div class="replace-order-detail">
    <!-- 工单基本信息 -->
    <div class="section">
      <h3 class="section-title">工单基本信息</h3>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">工单编码</span>
          <span class="value">{{ orderDetail.workOrderCode }}</span>
        </div>
        <div class="info-item">
          <span class="label">工单类型</span>
          <span class="value">{{ orderDetail.type }}</span>
        </div>
        <div class="info-item">
          <span class="label">紧急程度</span>
          <span class="value urgency-medium">{{ orderDetail.urgency }}</span>
        </div>
        <div class="info-item">
          <span class="label">工单状态</span>
          <span class="value status-processing">{{ orderDetail.status }}</span>
        </div>
        <div class="info-item">
          <span class="label">创建时间</span>
          <span class="value">{{ orderDetail.createTime }}</span>
        </div>
        <div class="info-item">
          <span class="label">当前处理人</span>
          <span class="value">{{ orderDetail.handler }}</span>
        </div>
      </div>
    </div>

    <!-- 关联方信息 -->
    <div class="section">
      <h3 class="section-title">关联方信息</h3>
      <div class="info-list">
        <div class="info-item">
          <span class="label">关联订单/阿姨</span>
          <div class="value-group">
            <span class="value">{{ orderDetail.relatedOrder }}</span>
            <span class="sub-value">阿姨: {{ orderDetail.practitioner }}</span>
          </div>
        </div>
        <div class="info-item">
          <span class="label">申请方</span>
          <span class="value">{{ orderDetail.applicant }}</span>
        </div>
        <div class="info-item">
          <span class="label">关联机构</span>
          <span class="value">{{ orderDetail.relatedAgency }}</span>
        </div>
      </div>
    </div>

    <!-- 服务任务管理 -->
    <div class="section">
      <h3 class="section-title">服务任务管理</h3>
      <div class="task-management-container">
        <!-- 任务统计信息 -->
        <div class="task-stats-grid">
          <!-- 加载状态 -->
          <div v-if="loading" class="task-stats-loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>加载任务统计中...</span>
          </div>

          <!-- 任务统计数据 -->
          <template v-else>
            <div class="task-stats-item">
              <span class="label">订单总任务数</span>
              <span class="value">{{ orderDetail.totalTasks || '0个' }}</span>
            </div>
            <div class="task-stats-item">
              <span class="label">已完成任务</span>
              <span class="value">{{ orderDetail.completedTasks || '0个' }}</span>
            </div>
            <div class="task-stats-item">
              <span class="label">待执行任务</span>
              <span class="value">{{ orderDetail.pendingTasks || '0个' }}</span>
            </div>
            <div class="task-stats-item">
              <span class="label">原服务人员</span>
              <span class="value">{{ orderDetail.practitioner || '未知' }}</span>
            </div>
          </template>
        </div>

        <!-- 查看详细任务列表按钮 -->
        <div class="task-actions">
          <el-button
            type="primary"
            @click="viewTaskList"
            class="task-list-btn"
            :disabled="isWorkOrderCompleted"
          >
            <i class="fas fa-list"></i>
            查看详细任务列表
          </el-button>
          <div class="placeholder"></div>
        </div>

        <!-- 重新指派起始日期 -->
        <div class="reassignment-date-section">
          <h5 class="section-title">重新指派起始日期</h5>
          <div class="date-picker-container">
            <div class="date-picker-wrapper">
              <el-date-picker
                v-model="reassignmentDate"
                type="date"
                placeholder="选择日期"
                format="YYYY/MM/DD"
                value-format="YYYY-MM-DD"
                class="date-picker"
                :disabled="isWorkOrderCompleted"
              />
            </div>
          </div>
          <div class="section-desc"> 从该日期开始的所有待执行任务将重新指派给新阿姨 </div>
        </div>
      </div>
    </div>

    <!-- 指派新阿姨 -->
    <div class="section">
      <h3 class="section-title">指派新阿姨</h3>
      <div class="assignment-form">
        <!-- 选择新阿姨 -->
        <div class="form-section">
          <h5 class="section-title">选择新阿姨</h5>
          <div class="select-container">
            <el-select
              v-model="selectedNewAuntie"
              placeholder="请选择新阿姨..."
              class="auntie-select"
              clearable
              filterable
              :disabled="isWorkOrderCompleted"
            >
              <el-option
                v-for="auntie in availableAunties"
                :key="auntie.id"
                :label="getAuntieDisplayLabel(auntie)"
                :value="auntie.id"
              >
                <div class="auntie-option-content">
                  <div class="auntie-main-info">
                    <span class="auntie-name">{{ auntie.name || '未知姓名' }}</span>
                    <span class="auntie-service-type"
                      >- {{ auntie.serviceType || '未知服务类型' }}</span
                    >
                    <span class="auntie-rating">- 评分{{ auntie.rating || '0' }}</span>
                    <span class="auntie-status"> -空闲中</span>
                  </div>
                </div>
              </el-option>
            </el-select>
          </div>
        </div>

        <!-- 指派说明 -->
        <div class="form-section">
          <h5 class="section-title">指派说明</h5>
          <div class="textarea-container">
            <el-input
              v-model="assignmentDescription"
              type="textarea"
              :rows="4"
              placeholder="请输入指派说明,如:原阿姨请假,由新阿姨顶岗..."
              class="description-textarea"
              :disabled="isWorkOrderCompleted"
            />
          </div>
        </div>

        <!-- 确认指派按钮 -->
        <div class="confirm-section">
          <el-button
            type="primary"
            @click="confirmReassignment"
            class="confirm-btn"
            :disabled="isWorkOrderCompleted"
          >
            <i class="fas fa-user-plus"></i>
            + 确认指派新阿姨
          </el-button>
          <div class="placeholder"></div>
        </div>
      </div>
    </div>

    <!-- 处理日志 -->
    <div class="section">
      <h3 class="section-title">处理日志</h3>
      <div class="log-content">
        <div class="timeline">
          <div class="timeline-line"></div>
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-wrapper">
            <div class="loading-spinner">
              <el-icon class="is-loading"><Loading /></el-icon>
              <span>加载中...</span>
            </div>
          </div>

          <!-- 日志列表 -->
          <div v-else-if="workOrderLogs.length > 0">
            <div v-for="(log, index) in workOrderLogs" :key="log.id || index" class="log-item">
              <div class="log-dot"></div>
              <div class="log-card">
                <div class="log-header">
                  <div class="log-type">
                    <i :class="getLogTypeIcon(log.logType)"></i>
                    {{ getLogTypeText(log.logType) }}
                  </div>
                  <div class="log-time">{{ log.createTimeStr }}</div>
                </div>
                <div class="log-details">
                  <div class="log-detail-item" v-if="log.operatorName">
                    <span class="label">操作人：</span>
                    <span class="value">{{ log.operatorName }}</span>
                  </div>
                  <div class="log-detail-item" v-if="log.logContent">
                    <span class="label">内容：</span>
                    <span class="value">{{ log.logContent }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-logs">
            <div class="empty-content">
              <el-icon class="empty-icon"><Document /></el-icon>
              <span>暂无处理日志</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 处理意见 -->
    <div class="section">
      <h3 class="section-title">处理意见</h3>
      <div class="comment-container">
        <div class="textarea-wrapper">
          <el-input
            v-model="processingComment"
            type="textarea"
            :rows="6"
            placeholder="在此输入您的处理意见或回复..."
            class="comment-textarea"
            :disabled="isWorkOrderCompleted"
          />
        </div>
        <div class="attachment-section">
          <el-button
            type="text"
            @click="addAttachment"
            class="attachment-btn"
            :loading="uploadLoading"
            :disabled="isWorkOrderCompleted"
          >
            <i class="fas fa-paperclip"></i>
            添加附件 (图片/文件)
          </el-button>

          <!-- 隐藏的文件输入框 -->
          <input
            ref="uploadRef"
            type="file"
            multiple
            accept="image/*,.pdf,.doc,.docx"
            style="display: none"
            @change="handleFileUpload"
            :disabled="isWorkOrderCompleted"
          />

          <!-- 附件列表 -->
          <div v-if="attachments.length > 0" class="attachments-list">
            <div v-for="attachment in attachments" :key="attachment.id" class="attachment-item">
              <div class="attachment-info">
                <div class="attachment-icon">
                  <i v-if="attachment.type.startsWith('image/')" class="fas fa-image"></i>
                  <i v-else-if="attachment.type === 'application/pdf'" class="fas fa-file-pdf"></i>
                  <i v-else class="fas fa-file-word"></i>
                </div>
                <div class="attachment-details">
                  <div class="attachment-name">{{ attachment.name }}</div>
                  <div class="attachment-meta">
                    {{ formatFileSize(attachment.size) }} • {{ attachment.uploadTime }}
                  </div>
                </div>
              </div>
              <div class="attachment-actions">
                <el-button
                  type="text"
                  size="small"
                  @click="removeAttachment(attachment.id)"
                  class="remove-btn"
                  :disabled="isWorkOrderCompleted"
                >
                  <i class="fas fa-trash"></i>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="action-buttons">
      <el-button @click="onClose">关闭</el-button>
      <el-button v-if="!isWorkOrderCompleted" type="primary" @click="onSubmitResult">
        提交处理结果
      </el-button>
    </div>

    <!-- 服务任务列表弹窗 -->
    <ServiceTaskList ref="serviceTaskListRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { ElMessage, ElIcon } from 'element-plus'
import { Loading, Document } from '@element-plus/icons-vue'
import ServiceTaskList from './ServiceTaskList.vue'
import {
  getWorkOrderDetail,
  getWorkOrderPractitioners,
  getTaskDetail,
  reassignTask,
  submitWorkOrderResolution,
  assignAunt,
  getWorkOrderLogs
} from '@/api/mall/employment/workOrder'
// 导入文件上传接口
import { updateFile } from '@/api/infra/file'

/** 定义props */
interface Props {
  task?: any
}

const props = withDefaults(defineProps<Props>(), {
  task: null
})

/** 定义emits */
const emit = defineEmits<{
  close: []
}>()

/** ServiceTaskList组件引用 */
const serviceTaskListRef = ref()

/** 工单详情数据 */
const orderDetail = ref({
  // 基本信息
  id: '',
  workOrderNo: '',
  orderNo: '',
  workOrderTitle: '',
  workOrderContent: '',
  workOrderType: '',
  priority: '',
  workOrderStatus: '',
  assigneeId: null,
  assigneeName: '',
  auntOneid: '',
  auntName: '',
  leaveType: null,
  startTime: '',
  endTime: '',
  durationHours: 0,
  durationDays: 0,
  status: 0,
  approveTime: '',
  approveRemark: '',
  complaintType: '',
  complaintLevel: '',
  complaintTime: '',
  customerExpectation: '',
  complainerId: null,
  complainerName: '',
  agencyId: null,
  agencyName: '',
  reassignmentStartDate: '',
  newAuntOneid: '',
  newAuntName: '',
  reassignmentDescription: '',
  reassignmentReason: '',
  reassignmentRemark: '',
  taskCount: 0,
  completedTaskCount: 0,
  pendingTaskCount: 0,
  remark: '',
  createTime: '',
  updateTime: '',

  // 前端展示字段映射
  taskNo: '',
  workOrderCode: '', // 工单编码（显示用）
  urgency: '',
  type: '',
  status: '',
  handler: '',
  relatedOrder: '',
  practitioner: '',
  applicant: '',
  relatedAgency: '',
  totalTasks: '',
  completedTasks: '',
  pendingTasks: ''
})

/** 重新指派日期 */
const reassignmentDate = ref('')

/** 选中的新阿姨 */
const selectedNewAuntie = ref('')

/** 指派说明 */
const assignmentDescription = ref('')

/** 可用阿姨列表 */
const availableAunties = ref<any[]>([])

/** 处理意见 */
const processingComment = ref('')

/** 附件列表 */
const attachments = ref<any[]>([])

/** 文件上传相关 */
const uploadRef = ref()
const uploadLoading = ref(false)

/** 加载状态 */
const loading = ref(false)

/** 工单日志 */
const workOrderLogs = ref<any[]>([])

/** 任务列表 */
const taskList = ref<any[]>([])

/** 计算属性：判断工单是否已完成（已解决或已关闭） */
const isWorkOrderCompleted = computed(() => {
  return (
    orderDetail.value.workOrderStatus === 'resolved' ||
    orderDetail.value.workOrderStatus === 'closed'
  )
})

/** 格式化日期时间为 yyyy-MM-dd hh:mm 格式 */
const formatDateTime = (timeStr: string) => {
  if (!timeStr) return ''
  try {
    const date = new Date(timeStr)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}`
  } catch (error) {
    return timeStr
  }
}

/** 生成阿姨显示标签 */
const getAuntieDisplayLabel = (auntie: any) => {
  if (!auntie) return ''
  return `${auntie.name || '未知姓名'}-${auntie.serviceType || '未知服务类型'}-${auntie.rating || '0'}分-空闲中`
}

/** 获取工单详情 */
const fetchOrderDetail = async (workOrderNo: string) => {
  try {
    loading.value = true
    const res = await getWorkOrderDetail(workOrderNo)
    const data = res

    // 映射接口数据到前端展示字段
    orderDetail.value = {
      ...orderDetail.value,
      ...data,
      // 前端展示字段映射
      taskNo: data.workOrderNo,
      workOrderCode: data.workOrderCode || data.code || data.workOrderNo || '',
      urgency: getPriorityText(data.priority),
      type: getWorkOrderTypeText(data.workOrderType),
      status: getWorkOrderStatusText(data.workOrderStatus),
      createTime: formatDateTime(data.createTime) || '',
      handler: data.assigneeName || '-',
      relatedOrder: data.orderNo,
      practitioner: data.auntName ? `${data.auntName}(${data.auntOneid})` : '-',
      applicant: data.complainerName ? `雇主: ${data.complainerName}` : '-',
      relatedAgency: data.agencyName,
      totalTasks: data.totalTasks || '0个',
      completedTasks: data.completedTasks || '0个',
      pendingTasks: data.pendingTasks || '0个'
    }

    // 设置重新指派日期为当前日期
    reassignmentDate.value = new Date().toISOString().split('T')[0]

    // 获取工单日志
    await fetchWorkOrderLogs(workOrderNo)

    // 获取任务详情和统计信息
    await fetchTaskDetail(orderDetail.value.relatedOrder)
  } catch (error) {
    console.error('获取换人申请工单详情失败:', error)
    ElMessage.error('获取工单详情失败')
  } finally {
    loading.value = false
  }
}

/** 获取可用阿姨列表 */
const fetchAvailableAunties = async () => {
  try {
    const res = await getWorkOrderPractitioners()
    availableAunties.value = res.map((item: any) => ({
      id: item.id || item.practitionerId || '', // 隐藏值，用于表单提交
      auntOneid: item.auntOneid || item.auntOneId || item.aunt_one_id || '', // 隐藏字段
      name: item.name || item.practitionerName || '未知姓名',
      serviceType: item.serviceType || item.serviceTypeName || '未知服务类型',
      rating: item.rating || item.score || '0',
      status: '空闲中'
    }))

    console.log('可用阿姨列表:', res)
  } catch (error) {
    console.error('获取可用阿姨列表失败:', error)
    ElMessage.error('获取阿姨列表失败')
  }
}

/** 获取工单日志 */
const fetchWorkOrderLogs = async (workOrderNo: string) => {
  try {
    const res = await getWorkOrderLogs(workOrderNo)
    workOrderLogs.value = res || []
  } catch (error) {
    console.error('获取工单日志失败:', error)
  }
}

/** 获取任务详情和统计信息 */
const fetchTaskDetail = async (orderNo: string) => {
  try {
    console.log('开始获取任务详情，订单号:', orderNo)
    const res = await getTaskDetail(orderNo)
    console.log('任务详情接口返回:', res)
    const taskData = res

    // 更新任务统计信息
    const totalTasks = taskData.taskCount || taskData.totalTaskCount || 0
    const completedTasks = taskData.completedTaskCount || taskData.completedTaskCount || 0
    const pendingTasks = taskData.pendingTaskCount

    // 更新orderDetail中的任务统计信息
    orderDetail.value.totalTasks = `${totalTasks}个`
    orderDetail.value.completedTasks = `${completedTasks}个`
    orderDetail.value.pendingTasks = `${pendingTasks}个`

    // 如果有任务列表数据，也保存到taskList
    if (taskData.taskList && Array.isArray(taskData.taskList)) {
      taskList.value = taskData.taskList
    } else if (taskData.tasks && Array.isArray(taskData.tasks)) {
      taskList.value = taskData.tasks
    }

    console.log('任务统计信息更新完成:', {
      totalTasks,
      completedTasks,
      pendingTasks,
      taskListLength: taskList.value.length
    })
  } catch (error) {
    console.error('获取任务详情失败:', error)
    ElMessage.error('获取任务详情失败')

    // 设置默认值
    orderDetail.value.totalTasks = '0个'
    orderDetail.value.completedTasks = '0个'
    orderDetail.value.pendingTasks = '0个'
  }
}

/** 关闭详情页 */
const onClose = () => {
  emit('close')
}

/** 监听task变化，更新详情数据 */
watch(
  () => props.task,
  (newTask) => {
    if (newTask) {
      // 如果有task数据，直接获取工单详情
      if (newTask.workOrderNo || newTask.id) {
        fetchOrderDetail(newTask.workOrderNo || newTask.id)
      } else {
        // 将task数据映射到orderDetail
        orderDetail.value = {
          ...orderDetail.value,
          taskNo: newTask.taskNo || newTask.id,
          workOrderCode:
            newTask.workOrderCode || newTask.code || newTask.taskNo || newTask.id || '',
          urgency: newTask.urgency === 'high' ? '高' : newTask.urgency === 'medium' ? '中' : '低',
          createTime: formatDateTime(newTask.createTime) || '',
          type: newTask.typeText || newTask.type || '换人申请',
          status: newTask.statusText || newTask.status || '处理中',
          handler: newTask.handler || '-',
          relatedOrder: newTask.orderNo || newTask.relatedOrder || '',
          practitioner: newTask.practitioner || newTask.relatedPractitioner || '',
          applicant: newTask.applicant || '',
          relatedAgency: newTask.agency || newTask.relatedAgency || '',
          totalTasks: newTask.totalTasks || '0个',
          completedTasks: newTask.completedTasks || '0个',
          pendingTasks: newTask.pendingTasks || '0个'
        }
      }
    }
  },
  { immediate: true }
)

/** 查看详细任务列表 */
const viewTaskList = () => {
  // 打开任务列表弹窗，传递订单号
  if (orderDetail.value.relatedOrder) {
    serviceTaskListRef.value?.openDialog(orderDetail.value.relatedOrder)
  } else {
    ElMessage.warning('订单号不存在，无法查看任务列表')
  }
}

/** 确认指派新阿姨 */
const confirmReassignment = async () => {
  if (!selectedNewAuntie.value) {
    ElMessage.warning('请选择新阿姨')
    return
  }

  if (!reassignmentDate.value) {
    ElMessage.warning('请选择重新指派起始日期')
    return
  }

  try {
    // 获取选中的阿姨信息
    const selectedAuntie = availableAunties.value.find(
      (auntie) => auntie.id === selectedNewAuntie.value
    )

    // 调用指派阿姨接口
    const assignData = {
      workOrderNo: orderDetail.value.taskNo,
      practitionerId: selectedNewAuntie.value,
      auntOneid: selectedAuntie?.auntOneid || '',
      auntName: selectedAuntie?.name || '',
      reassignmentTime: reassignmentDate.value,
      assignmentReason: assignmentDescription.value,
      orderNo: orderDetail.value.relatedOrder
    }

    await assignAunt(assignData)

    ElMessage.success('指派成功！')

    // 回到父页面
    onClose()
  } catch (error) {
    console.error('指派失败:', error)
    ElMessage.error('指派失败，请重试')
  }
}

/** 添加附件 */
const addAttachment = () => {
  uploadRef.value?.click()
}

/** 文件上传前处理 */
const beforeUpload = (file: File) => {
  // 检查文件类型
  const allowedTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ]
  const isValidType = allowedTypes.includes(file.type)

  if (!isValidType) {
    ElMessage.error('只支持图片、PDF和Word文档格式！')
    return false
  }

  // 检查文件大小 (10MB)
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB！')
    return false
  }

  return true
}

/** 文件上传处理 */
const handleFileUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files

  if (!files || files.length === 0) return

  uploadLoading.value = true

  try {
    // 逐个上传文件
    for (let i = 0; i < files.length; i++) {
      const file = files[i]

      if (!beforeUpload(file)) {
        continue
      }

      // 创建FormData，参考套餐主图上传的实现
      const uploadFormData = new FormData()
      uploadFormData.append('file', file)

      // 调用文件上传接口
      const response = await updateFile(uploadFormData)

      if (response && response.data) {
        const attachment = {
          id: Date.now() + i,
          name: file.name,
          size: file.size,
          type: file.type,
          url: response.data,
          uploadTime: new Date().toLocaleString()
        }

        attachments.value.push(attachment)
        ElMessage.success(`文件 ${file.name} 上传成功`)
      }
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    ElMessage.error('文件上传失败，请重试')
  } finally {
    uploadLoading.value = false
    // 清空input值，允许重复上传同一文件
    target.value = ''
  }
}

/** 删除附件 */
const removeAttachment = (attachmentId: number) => {
  const index = attachments.value.findIndex((item) => item.id === attachmentId)
  if (index > -1) {
    attachments.value.splice(index, 1)
    ElMessage.success('附件删除成功')
  }
}

/** 格式化文件大小 */
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/** 根据文件类型获取文件分类 */
const getFileCategory = (fileType: string) => {
  if (fileType?.startsWith('image/')) {
    return 'evidence' // 图片作为证据材料
  } else if (fileType === 'application/pdf') {
    return 'document' // PDF作为文档
  } else if (fileType?.includes('word') || fileType?.includes('document')) {
    return 'document' // Word文档作为文档
  } else {
    return 'other' // 其他类型
  }
}

/** 获取日志类型图标 */
const getLogTypeIcon = (logType: string) => {
  const iconMap = {
    creation: 'fas fa-file-alt',
    status_change: 'fas fa-exchange-alt',
    assignment: 'fas fa-user-plus',
    comment: 'fas fa-comment',
    resolution: 'fas fa-check-circle',
    reassign: 'fas fa-user-edit',
    default: 'fas fa-info-circle'
  }
  return iconMap[logType] || iconMap.default
}

/** 获取日志类型文本 */
const getLogTypeText = (logType: string) => {
  const typeMap = {
    creation: '工单创建',
    status_change: '状态变更',
    assignment: '分配处理人',
    comment: '处理意见',
    resolution: '处理结果',
    reassign: '重新指派',
    default: '操作记录'
  }
  return typeMap[logType] || logType
}

/** 工具函数：获取工单类型文本 */
const getWorkOrderTypeText = (type: string) => {
  const typeMap = {
    complaint: '投诉',
    substitution_request: '换人申请',
    take_leave: '请假/顶岗',
    leave_adjustment: '调休',
    separation_application: '离职申请'
  }
  return typeMap[type] || type
}

/** 工具函数：获取工单状态文本 */
const getWorkOrderStatusText = (status: string) => {
  const statusMap = {
    pending: '待处理',
    processing: '处理中',
    resolved: '已解决',
    closed: '已关闭',
    approved: '已批准',
    rejected: '已驳回'
  }
  return statusMap[status] || status
}

/** 工具函数：获取优先级文本 */
const getPriorityText = (priority: string) => {
  const priorityMap = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return priorityMap[priority] || priority
}

/** 提交处理结果 */
const onSubmitResult = async () => {
  if (!processingComment.value.trim()) {
    ElMessage.warning('请输入处理意见')
    return
  }

  try {
    // 构建附件数组，按照接口文档的字段要求
    const attachmentsArray = attachments.value.map((item) => ({
      fileName: item.name,
      fileUrl: item.url,
      fileType: item.type,
      fileCategory: getFileCategory(item.type), // 根据文件类型判断分类
      uploadPurpose: '处理结果附件'
    }))

    const data = {
      workOrderNo: orderDetail.value.workOrderNo,
      logType: 'resolution',
      logContent: processingComment.value,
      attachments: attachmentsArray
    }

    const res = await submitWorkOrderResolution(data)
    ElMessage.success('处理结果提交成功')
    // 重新获取工单详情
    await fetchOrderDetail(orderDetail.value.workOrderNo)
    processingComment.value = ''
    // 清空附件列表
    attachments.value = []
  } catch (error) {
    console.error('提交处理结果失败:', error)
    ElMessage.error('提交失败，请重试')
  }
}

onMounted(async () => {
  // 获取可用阿姨列表
  await fetchAvailableAunties()

  // 如果有task数据，直接使用；否则获取默认数据
  if (props.task) {
    // task数据会在watch中处理
  } else {
    // TODO: 从路由参数获取工单ID
    const orderId = 'GD20240627002'
    await fetchOrderDetail(orderId)
  }
})
</script>

<style scoped lang="scss">
.replace-order-detail {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
  background: white;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;

    .page-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      width: 32px;
      height: 32px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: #e9ecef;
      }

      i {
        font-size: 14px;
        color: #666;
      }
    }
  }

  .section {
    margin-bottom: 30px;

    .section-title {
      margin: 15px 0 15px 0;
      font-weight: 600;
      color: #333;
      padding-bottom: 8px;
      border-bottom: 1px solid #e9ecef;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px 30px;

      .info-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .label {
          font-weight: 500;
          color: #666;
          margin-bottom: 8px;
          font-size: 14px;
        }

        .value {
          color: #333;
          font-size: 16px;
          font-weight: 500;

          &.urgency-medium {
            background: #ff9800;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            width: fit-content;
          }

          &.status-processing {
            background: #ff9800;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            width: fit-content;
          }
        }
      }
    }

    .info-list {
      .info-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-weight: 500;
          color: #666;
          margin-bottom: 8px;
          font-size: 14px;
        }

        .value-group {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .value {
            color: #333;
            font-weight: 600;
            font-size: 16px;
          }

          .sub-value {
            color: #999;
            font-size: 13px;
            margin-left: 0;
          }
        }

        .value {
          color: #333;
          font-size: 16px;
          font-weight: 500;
        }
      }
    }
    // 处理日志样式
    .log-content {
      .timeline {
        position: relative;
        padding-left: 20px;
        max-height: 300px; // 限制最大高度，大约3条日志的高度
        overflow-y: auto; // 添加垂直滚动条

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;

          &:hover {
            background: #a8a8a8;
          }
        }

        .timeline-line {
          position: absolute;
          left: 10px;
          top: 0;
          bottom: 0;
          width: 2px;
          background: #e9ecef;
        }

        // 加载状态
        .loading-wrapper {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 40px 0;

          .loading-spinner {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            color: #666;

            .el-icon {
              font-size: 24px;
              color: #409eff;
            }

            span {
              font-size: 14px;
            }
          }
        }

        // 空状态
        .empty-logs {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 40px 0;

          .empty-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            color: #999;

            .empty-icon {
              font-size: 32px;
              color: #c0c4cc;
            }

            span {
              font-size: 14px;
            }
          }
        }

        .log-item {
          position: relative;
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }

          .log-dot {
            position: absolute;
            left: -15px;
            top: 20px;
            width: 12px;
            height: 12px;
            background: #409eff;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #409eff;
          }

          .log-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin-left: 20px;

            .log-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;

              .log-type {
                display: flex;
                align-items: center;
                gap: 8px;
                font-weight: 600;
                color: #333;
                font-size: 14px;

                i {
                  color: #409eff;
                  font-size: 14px;
                }
              }

              .log-time {
                font-size: 12px;
                color: #999;
              }
            }

            .log-details {
              .log-detail-item {
                display: flex;
                margin-bottom: 8px;

                &:last-child {
                  margin-bottom: 0;
                }

                .label {
                  font-weight: 500;
                  color: #666;
                  min-width: 60px;
                  margin-right: 8px;
                }

                .value {
                  color: #333;
                  flex: 1;
                }
              }
            }
          }
        }
      }
    }
    // 处理意见样式
    .comment-container {
      .textarea-wrapper {
        margin-bottom: 12px;

        .comment-textarea {
          :deep(.el-textarea__inner) {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            resize: vertical;
            min-height: 120px;
            font-size: 14px;
          }
        }
      }

      .attachment-section {
        .attachment-btn {
          color: #409eff;
          font-size: 13px;
          padding: 0;

          i {
            margin-right: 6px;
          }

          &:hover {
            color: #66b1ff;
          }
        }

        .attachments-list {
          margin-top: 12px;

          .attachment-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 6px;
            margin-bottom: 8px;
            border: 1px solid #e9ecef;

            &:last-child {
              margin-bottom: 0;
            }

            .attachment-info {
              display: flex;
              align-items: center;
              flex: 1;

              .attachment-icon {
                width: 32px;
                height: 32px;
                background: #e3f2fd;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 12px;

                i {
                  color: #1976d2;
                  font-size: 14px;
                }
              }

              .attachment-details {
                flex: 1;

                .attachment-name {
                  font-weight: 500;
                  color: #333;
                  font-size: 14px;
                  margin-bottom: 4px;
                  word-break: break-all;
                }

                .attachment-meta {
                  color: #666;
                  font-size: 12px;
                }
              }
            }

            .attachment-actions {
              .remove-btn {
                color: #f56c6c;
                padding: 4px;

                &:hover {
                  color: #f78989;
                }

                i {
                  font-size: 12px;
                }
              }
            }
          }
        }
      }
    }

    .task-management-container {
      .task-stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px 30px;
        margin-bottom: 20px;

        // 加载状态样式
        .task-stats-loading {
          grid-column: 1 / -1;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 40px 0;
          gap: 12px;
          color: #666;

          .el-icon {
            font-size: 24px;
            color: #409eff;
          }

          span {
            font-size: 14px;
          }
        }

        .task-stats-item {
          display: flex;
          flex-direction: column;
          align-items: flex-start;

          .label {
            font-weight: 500;
            color: #666;
            margin-bottom: 8px;
            font-size: 14px;
          }

          .value {
            color: #333;
            font-size: 16px;
            font-weight: 500;
          }
        }
      }

      .task-actions {
        margin-bottom: 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .placeholder {
          flex: 1;
        }

        .task-list-btn {
          width: auto;
          height: 40px;
          font-size: 14px;
          min-width: 160px;

          i {
            margin-right: 8px;
          }
        }
      }

      .reassignment-date-section {
        margin-bottom: 25px;

        .section-title {
          margin: 0 0 12px 0;
          font-size: 14px;
          font-weight: 600;
          color: #333;
          padding-bottom: 0;
          border-bottom: none;
        }

        .date-picker-container {
          margin-bottom: 8px;

          .date-picker-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;

            .date-picker {
              width: 100%;
            }
          }
        }

        .section-desc {
          color: #666;
          font-size: 13px;
          line-height: 1.5;
          margin-top: 8px;
        }
      }

      .assignment-form {
        .form-section {
          margin-bottom: 24px;

          &:last-child {
            margin-bottom: 0;
          }

          .section-title {
            margin: 0 0 12px 0;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            padding-bottom: 0;
            border-bottom: none;
          }

          .select-container {
            .auntie-select {
              width: 100%;

              :deep(.el-input__wrapper) {
                border: 1px solid #d9d9d9;
                border-radius: 4px;
              }

              :deep(.el-select__tags) {
                .el-tag {
                  background: #e8f5e8;
                  border-color: #b7eb8f;
                  color: #52c41a;
                }
              }
            }
          }

          .textarea-container {
            .description-textarea {
              :deep(.el-textarea__inner) {
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                resize: vertical;
                min-height: 80px;
              }
            }
          }
        }

        .confirm-section {
          margin-bottom: 25px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .confirm-btn {
            width: auto;
            height: 40px;
            font-size: 14px;
            min-width: 160px;

            i {
              margin-right: 8px;
            }
          }

          .placeholder {
            flex: 1;
          }
        }
      }

      // 阿姨选择器样式
      :deep(.el-select-dropdown) {
        .auntie-option-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          padding: 4px 0;

          .auntie-main-info {
            display: flex;
            align-items: center;
            gap: 4px;
            flex: 1;

            .auntie-name {
              font-weight: 600;
              color: #333;
              font-size: 14px;
            }

            .auntie-service-type {
              color: #666;
              font-size: 13px;
            }

            .auntie-rating {
              color: #ff9800;
              font-weight: 500;
              font-size: 13px;
            }
          }

          .auntie-status {
            .status-badge {
              padding: 2px 8px;
              border-radius: 12px;
              font-size: 12px;
              font-weight: 500;

              &.status-available {
                background: #e8f5e8;
                color: #52c41a;
                border: 1px solid #b7eb8f;
              }

              &.status-busy {
                background: #fff2e8;
                color: #fa8c16;
                border: 1px solid #ffd591;
              }

              &.status-offline {
                background: #f5f5f5;
                color: #999;
                border: 1px solid #d9d9d9;
              }
            }
          }
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;

    .el-button {
      min-width: 80px;
    }
  }

  // 禁用状态样式
  .el-button:disabled,
  .el-input.is-disabled .el-input__inner,
  .el-textarea.is-disabled .el-textarea__inner,
  .el-select.is-disabled .el-input__wrapper {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .attachment-btn:disabled {
    color: #c0c4cc !important;
    cursor: not-allowed;

    &:hover {
      color: #c0c4cc !important;
    }
  }

  .remove-btn:disabled {
    color: #c0c4cc !important;
    cursor: not-allowed;

    &:hover {
      color: #c0c4cc !important;
    }
  }

  .task-list-btn:disabled,
  .confirm-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;

    &:hover {
      opacity: 0.6;
    }
  }

  // 全局加载状态
  .loading-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0;

    .loading-spinner {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      color: #666;

      .el-icon {
        font-size: 24px;
        color: #409eff;
      }

      span {
        font-size: 14px;
      }
    }
  }

  // 空状态
  .empty-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0;

    .empty-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      color: #999;

      .empty-icon {
        font-size: 32px;
        color: #c0c4cc;
      }

      span {
        font-size: 14px;
      }
    }
  }
}
</style>
