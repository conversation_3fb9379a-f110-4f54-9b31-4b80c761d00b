<!--
  页面名称：基本信息标签页
  功能描述：展示家政服务订单的基本信息
-->
<template>
  <div class="basic-info-tab">
    <div class="info-content">
      <!-- 客户信息 -->
      <div class="info-section">
        <h3 class="section-title">客户信息</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>客户姓名：</label>
              <span>{{ orderDetail.customerName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>联系电话：</label>
              <span>{{ orderDetail.customerPhone || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>服务地址：</label>
              <span class="address-text">{{ orderDetail.serviceAddress || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 服务信息 -->
      <div class="info-section">
        <h3 class="section-title">服务信息</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>服务类型：</label>
              <span>{{ orderDetail.serviceType || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>服务套餐：</label>
              <span>{{ orderDetail.servicePackage || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>预约时间：</label>
              <span>{{ formatDateTime(orderDetail.appointmentTime) }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>服务开始：</label>
              <span>{{ formatDateTime(orderDetail.serviceStartDate) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>服务结束：</label>
              <span>{{ formatDateTime(orderDetail.serviceEndDate) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>服务频次：</label>
              <span>{{ orderDetail.serviceFrequency || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 人员信息 -->
      <div class="info-section">
        <h3 class="section-title">人员信息</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>服务人员：</label>
              <span>{{ orderDetail.practitionerName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>人员电话：</label>
              <span>{{ orderDetail.practitionerPhone || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>服务机构：</label>
              <span>{{ orderDetail.agencyName || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 金额信息 -->
      <div class="info-section">
        <h3 class="section-title">金额信息</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>订单金额：</label>
              <span class="amount">¥{{ formatAmount(orderDetail.totalAmount) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>实际金额：</label>
              <span class="amount">¥{{ formatAmount(orderDetail.actualAmount) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>优惠金额：</label>
              <span class="amount">¥{{ formatAmount(orderDetail.discountAmount) }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 其他信息 -->
      <div class="info-section">
        <h3 class="section-title">其他信息</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>订单状态：</label>
              <span>{{ getOrderStatusText(orderDetail.orderStatus) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>支付状态：</label>
              <span>{{ getPaymentStatusText(orderDetail.paymentStatus) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ formatDateTime(orderDetail.createTime) }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="orderDetail.remark">
          <el-col :span="24">
            <div class="info-item">
              <label>备注信息：</label>
              <span class="remark-text">{{ orderDetail.remark }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { InfoFilled } from '@element-plus/icons-vue'

interface Props {
  orderDetail: any
}

defineProps<Props>()

const formatAmount = (amount: number): string => {
  if (!amount) return '0'
  return amount.toLocaleString()
}

const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return '-'
  try {
    const date = new Date(dateTime)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return dateTime
  }
}

const getOrderStatusText = (status: string): string => {
  if (!status) return '-'
  const statusMap: Record<string, string> = {
    pending: '待派单',
    assigned: '已派单',
    in_service: '服务中',
    completed: '已完成',
    cancelled: '已取消',
    PENDING: '待支付',
    PAID: '已支付',
    COMPLETED: '已完成',
    CANCELLED: '已取消',
    REFUNDED: '已退款'
  }
  return statusMap[status] || status
}

const getPaymentStatusText = (status: string): string => {
  if (!status) return '-'
  const statusMap: Record<string, string> = {
    unpaid: '未支付',
    paid: '已支付',
    partial: '部分支付',
    UNPAID: '未支付',
    PAID: '已支付',
    REFUNDED: '已退款'
  }
  return statusMap[status] || status
}
</script>

<style scoped lang="scss">
.basic-info-tab {
  .info-content {
    .info-section {
      margin-bottom: 24px;
      padding: 16px;
      background-color: #fafafa;
      border-radius: 8px;
      border: 1px solid #e4e7ed;

      .section-title {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        border-bottom: 2px solid #409eff;
        padding-bottom: 8px;
      }

      .info-item {
        margin-bottom: 16px;
        display: flex;
        align-items: flex-start;

        label {
          min-width: 80px;
          color: #606266;
          font-weight: 500;
          margin-right: 8px;
        }

        span {
          color: #303133;
          flex: 1;

          &.amount {
            color: #f56c6c;
            font-weight: 500;
          }

          &.address-text {
            word-break: break-all;
            line-height: 1.5;
            white-space: pre-wrap;
            word-wrap: break-word;
          }

          &.remark-text {
            word-break: break-all;
            line-height: 1.5;
            white-space: pre-wrap;
            word-wrap: break-word;
            color: #666;
          }
        }
      }
    }
  }
}
</style>
