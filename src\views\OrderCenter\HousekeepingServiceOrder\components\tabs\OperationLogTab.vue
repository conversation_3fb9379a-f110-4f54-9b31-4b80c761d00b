<!--
  页面名称：操作日志标签页
  功能描述：展示家政服务订单的操作日志
-->
<template>
  <div class="operation-log-tab">
    <div class="section-header">
      <div class="section-title">
        <el-icon><Document /></el-icon>
        操作日志
      </div>
    </div>

    <div class="log-content">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      <div v-else-if="operationLogs.length > 0" class="log-timeline">
        <el-timeline>
          <el-timeline-item
            v-for="(log, index) in operationLogs"
            :key="log.id || index"
            :timestamp="formatDateTime(log.createTime)"
            placement="top"
            :color="getLogColor(log.logType || log.operationType)"
          >
            <div class="log-item">
              <div class="log-header">
                <span class="log-user"
                  >{{ log.operatorName || log.actorName || '系统' }} ({{
                    log.operatorRole || log.actorRole || '管理员'
                  }})</span
                >
                <el-button
                  :type="getButtonType(log.logType || log.operationType) as any"
                  size="small"
                  class="log-button"
                >
                  {{ getButtonText(log.logType || log.operationType) }}
                </el-button>
              </div>
              <div class="log-content-text">
                <div class="log-description">{{
                  log.logTitle || log.operationDescription || '操作记录'
                }}</div>
                <div
                  v-if="
                    parsedLogs[log.id] &&
                    parsedLogs[log.id].details &&
                    parsedLogs[log.id].details.length > 0
                  "
                  class="log-details"
                >
                  <div
                    v-for="(detail, detailIndex) in parsedLogs[log.id].details"
                    :key="detailIndex"
                    class="detail-item"
                  >
                    {{ detail }}
                  </div>
                </div>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
      <div v-else class="no-logs">
        <el-empty description="暂无操作日志" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, View } from '@element-plus/icons-vue'
import { HousekeepingServiceOrderApi } from '@/api/OrderCenter/HousekeepingServiceOrder'
import { parseOperationLog, formatFieldName, getLogSummary } from '@/utils/log-parser'

interface Props {
  orderDetail: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'view-full-log': []
}>()

// 响应式数据
const loading = ref(false)
const operationLogs = ref<any[]>([])
const parsedLogs = ref<Record<string, any>>({})

// 获取操作日志
const fetchOperationLogs = async () => {
  if (!props.orderDetail?.id) return

  loading.value = true
  try {
    // 修复API调用参数，使用正确的参数结构
    const orderNo = props.orderDetail.orderNumber || props.orderDetail.orderNo || ''
    console.log('获取操作日志，订单号:', orderNo)

    const result = await HousekeepingServiceOrderApi.getOperationLogList({
      orderNo: orderNo,
      page: 1,
      size: 10
    })

    console.log('操作日志API响应:', result)

    // 修复数据结构处理，支持多种返回格式
    const resultAny = result as any
    if (resultAny && (resultAny.list || resultAny.records || resultAny.data)) {
      // 支持多种数据结构
      const logList = resultAny.list || resultAny.records || resultAny.data || []
      operationLogs.value = logList.map((log: any) => ({
        id: log.id || log.logId || '',
        logType: log.logType || log.operationType || log.type || '',
        logTitle: log.logTitle || log.operationDescription || log.description || '操作记录',
        logContent: log.logContent || log.content || log.operationContent || '',
        oldStatus: log.oldStatus || '',
        newStatus: log.newStatus || '',
        operatorName: log.operatorName || log.actorName || log.operator || '系统',
        operatorRole: log.operatorRole || log.actorRole || log.role || '管理员',
        createTime: log.createTime || log.operationTime || log.timestamp || ''
      }))
    } else {
      operationLogs.value = []
    }

    // 解析日志内容
    parseAllLogs()

    console.log('操作日志获取成功:', operationLogs.value)
  } catch (error) {
    console.error('获取操作日志失败:', error)
    ElMessage.error('获取操作日志失败')
    operationLogs.value = []
  } finally {
    loading.value = false
  }
}

// 解析所有日志内容
const parseAllLogs = () => {
  parsedLogs.value = {}
  operationLogs.value.forEach((log) => {
    if (log.logContent && log.logType !== '订单创建') {
      const parsedResult = parseOperationLog(log.logContent, 'housekeeping')
      parsedLogs.value[log.id] = {
        summary: getLogSummary(log.logType, parsedResult.changes),
        details: parsedResult.changes.map(
          (change) => `${formatFieldName(change.field)}: ${change.oldValue} → ${change.newValue}`
        ),
        hasChanges: parsedResult.hasChanges
      }
    }
  })
}

// 获取日志颜色
const getLogColor = (logType: string) => {
  const colorMap: Record<string, string> = {
    订单创建: '#67c23a',
    订单编辑: '#e6a23c',
    审批通过: '#67c23a',
    审批驳回: '#f56c6c',
    确认收款: '#409eff',
    订单完成: '#67c23a',
    系统管理员: '#909399',
    create: '#67c23a',
    update: '#e6a23c',
    delete: '#f56c6c',
    payment: '#409eff',
    status_change: '#909399'
  }
  return colorMap[logType] || '#909399'
}

// 获取按钮类型
const getButtonType = (logType: string) => {
  const typeMap: Record<string, string> = {
    订单创建: 'success',
    订单编辑: 'warning',
    审批通过: 'success',
    审批驳回: 'danger',
    确认收款: 'primary',
    订单完成: 'success',
    系统管理员: 'info',
    create: 'success',
    update: 'warning',
    delete: 'danger',
    payment: 'primary',
    status_change: 'info'
  }
  return typeMap[logType] || 'info'
}

// 获取按钮文本
const getButtonText = (logType: string) => {
  return logType || '其他'
}

// 监听订单详情变化
watch(
  () => props.orderDetail,
  (newVal) => {
    if (newVal?.id) {
      fetchOperationLogs()
    }
  },
  { immediate: true }
)

// 查看完整日志
const handleViewFullLog = () => {
  emit('view-full-log')
}

// 格式化日期时间
const formatDateTime = (timestamp: number): string => {
  if (!timestamp) return '-'
  try {
    const date = new Date(timestamp)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return '-'
  }
}

// 组件挂载时获取数据
// 移除重复的onMounted钩子，避免重复调用
// onMounted(() => {
//   if (props.orderDetail?.id) {
//     fetchOperationLogs()
//   }
// })
</script>

<style scoped lang="scss">
.operation-log-tab {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e4e7ed;

    .section-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      color: #303133;

      .el-icon {
        margin-right: 8px;
        color: #409eff;
      }
    }
  }

  .log-content {
    .loading-container {
      padding: 20px;
    }

    .log-timeline {
      .log-item {
        background-color: #fff;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .log-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .log-user {
            font-weight: 500;
            color: #333;
            margin-right: 15px;
          }

          .log-button {
            flex-shrink: 0;
          }
        }

        .log-content-text {
          .log-description {
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
          }

          .log-details {
            margin-top: 10px;
            padding-left: 20px;
            border-left: 2px solid #e9e9eb;

            .detail-item {
              font-size: 14px;
              color: #666;
              margin-bottom: 5px;
              line-height: 1.5;
            }
          }
        }
      }
    }

    .no-logs {
      padding: 40px 0;
      text-align: center;
    }
  }
}

:deep(.el-timeline-item__node) {
  background-color: #409eff;
}

:deep(.el-timeline-item__timestamp) {
  color: #909399;
  font-size: 12px;
}
</style>
