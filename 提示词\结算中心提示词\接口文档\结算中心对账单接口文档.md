# 结算中心对账单接口文档

## 1. 接口概述和基础信息

### 1.1 基础信息
- **基础路径**: `/publicbiz/reconciliation`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **请求方式**: GET/POST/PUT/DELETE

### 1.2 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-12-01 10:30:00"
}
```

### 1.3 响应状态码说明
- **200**: 请求成功
- **400**: 参数验证错误
- **403**: 权限不足
- **404**: 资源不存在
- **500**: 服务器错误

## 2. 接口详细说明

### 2.1 获取对账单列表

**接口地址**: `GET /publicbiz/reconciliation/pageReconciliationList`

**功能说明**: 分页查询对账单列表，支持多条件筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNo | Integer | 是 | 当前页码，从1开始 |
| pageSize | Integer | 是 | 每页大小，默认10 |
| statementNo | String | 否 | 对账单号，支持模糊查询 |
| agencyName | String | 否 | 机构名称，支持模糊查询 |
| status | String | 否 | 对账状态：pending-待对账确认/confirmed-已确认/paid-已支付/cancelled-已取消 |
| startDate | String | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | String | 否 | 结束日期，格式：YYYY-MM-DD |


**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码 |
| message | String | 响应消息 |
| data | Array | 对账单列表 |
| total | Integer | 总记录数 |
| pageNo | Integer | 当前页码 |
| pageSize | Integer | 每页大小 |
| data[].id | Long | 对账单ID |
| data[].statementNo | String | 对账单号 |
| data[].statementType | String | 对账单类型：agency-机构对账/platform-平台对账/practitioner-阿姨对账 |
| data[].generationTime | String | 生成时间 |
| data[].agencyId | Long | 机构ID |
| data[].agencyName | String | 机构名称 |
| data[].practitionerOneid | String | 阿姨OneID |
| data[].practitionerName | String | 阿姨姓名 |
| data[].totalAmount | BigDecimal | 对账总金额 |
| data[].agencyAmount | BigDecimal | 机构分成金额 |
| data[].platformAmount | BigDecimal | 平台分成金额 |
| data[].orderCount | Integer | 包含订单数量 |
| data[].orderList | String | 包含订单列表（JSON格式） |
| data[].reconciliationStatus | String | 对账状态 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "statementNo": "ST20241201001",
      "statementType": "agency",
      "generationTime": "2024-12-01 10:00:00",
      "agencyId": 1001,
      "agencyName": "北京家政服务有限公司",
      "practitionerOneid": null,
      "practitionerName": null,
      "totalAmount": 15000.00,
      "agencyAmount": 12000.00,
      "platformAmount": 3000.00,
      "orderCount": 5,
      "orderList": "[\"DD20241201001\",\"DD20241201002\",\"DD20241201003\"]",
      "reconciliationStatus": "pending"
    }
  ],
  "total": 15,
  "pageNo": 1,
  "pageSize": 10,
  "timestamp": "2024-12-01 10:30:00"
}
```

### 2.2 获取对账单详情

**接口地址**: `GET /publicbiz/reconciliation/getReconciliationDetail`

**功能说明**: 根据对账单号获取对账单详细信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| statementNo | String | 是 | 对账单号 |

**请求示例**:
```bash
GET /publicbiz/reconciliation/getReconciliationDetail?statementNo=ST20241201001
```

**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码 |
| message | String | 响应消息 |
| data | Object | 对账单详细信息 |
| data.id | Long | 对账单ID |
| data.statementNo | String | 对账单号 |
| data.generationTime | String | 生成时间 |
| data.agencyId | Long | 机构ID |
| data.agencyName | String | 机构名称 |
| data.agencyContact | String | 机构联系人 |
| data.agencyPhone | String | 机构联系电话 |
| data.totalAmount | BigDecimal | 对账总金额 |
| data.agencyAmount | BigDecimal | 机构分成金额 |
| data.platformAmount | BigDecimal | 平台分成金额 |
| data.agencyRatio | BigDecimal | 机构分成比例(%) |
| data.platformRatio | BigDecimal | 平台分成比例(%) |
| data.orderCount | Integer | 包含订单数量 |
| data.orderList | String | 包含订单列表（JSON格式） |
| data.reconciliationStatus | String | 对账状态 |
| data.confirmationTime | String | 确认时间 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "statementNo": "ST20241201001",
    "generationTime": "2024-12-01 10:00:00",
    "agencyId": 1001,
    "agencyName": "北京家政服务有限公司",
    "agencyContact": "张经理",
    "agencyPhone": "010-12345678",
    "totalAmount": 15000.00,
    "agencyAmount": 12000.00,
    "platformAmount": 3000.00,
    "agencyRatio": 80.00,
    "platformRatio": 20.00,
    "orderCount": 5,
    "orderList": "[\"DD20241201001\",\"DD20241201002\",\"DD20241201003\"]",
    "reconciliationStatus": "pending",
    "confirmationTime": null
    ]
  },
  "timestamp": "2024-12-01 10:30:00"
}
```

### 2.3 确认对账

**接口地址**: `POST /publicbiz/reconciliation/confirmReconciliation`

**功能说明**: 确认对账单，支持调整分成比例

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| statementNo | String | 是 | 对账单号 |
| enableAdjustment | Boolean | 是 | 是否启用手工调整 |
| adjustedAgencyAmount | BigDecimal | 否 | 调整后机构分成金额（启用调整时必填） |
| adjustedPlatformAmount | BigDecimal | 否 | 调整后平台分成金额（启用调整时必填） |
| adjustmentReason | String | 否 | 调整原因（启用调整时必填） |

**请求示例**:
```json
{
  "statementNo": "ST20241201001",
  "enableAdjustment": true,
  "adjustedAgencyAmount": 11500.00,
  "adjustedPlatformAmount": 3500.00,
  "adjustmentReason": "因服务质量问题，调整分成比例"
}
```

**响应字段**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| data.statementNo | String | 对账单号 |
| data.confirmationTime | String | 确认时间 |
| data.operatorId | Long | 操作人ID |
| data.operatorName | String | 操作人姓名 |

**响应示例**:
```json
{
  "code": 200,
  "message": "对账确认成功",
  "data": {
    "statementNo": "ST20241201001",
    "confirmationTime": "2024-12-01 11:00:00",
    "operatorId": 1001,
    "operatorName": "张三"
  },
  "timestamp": "2024-12-01 11:00:00"
}
```

### 2.4 导出对账单

**接口地址**: `GET /publicbiz/reconciliation/exportStatement`

**功能说明**: 导出对账单为Excel文件

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| statementNo | String | 是 | 对账单号 |
| exportType | String | 否 | 导出类型：summary-汇总/detail-明细，默认detail |

**请求示例**:
```bash
GET /publicbiz/reconciliation/exportStatement?statementNo=ST20241201001&exportType=detail
```

**响应**: 返回Excel文件流

## 3. 数据字典

### 3.1 对账单类型（statementType）
- **agency**: 机构对账
- **platform**: 平台对账
- **practitioner**: 阿姨对账

### 3.2 对账状态（reconciliationStatus）
- **pending**: 待对账确认
- **confirmed**: 已确认
- **paid**: 已支付
- **cancelled**: 已取消

### 3.3 导出类型（exportType）
- **summary**: 汇总 - 导出对账单汇总信息
- **detail**: 明细 - 导出对账单详细订单信息

### 3.4 调整标识（enableAdjustment）
- **true**: 启用手工调整 - 允许调整分成金额
- **false**: 不调整 - 使用系统默认分成比例

### 3.5 响应状态码（code）
- **200**: 请求成功
- **400**: 参数验证错误
- **403**: 权限不足
- **404**: 资源不存在
- **500**: 服务器错误

### 3.6 业务错误码
- **1001**: 对账单号不存在
- **1002**: 对账单状态不允许操作
- **1003**: 分成金额调整超出允许范围
- **1004**: 对账单已存在，不能重复生成
- **1005**: 没有符合条件的订单数据
- **1006**: 导出文件生成失败

## 4. 错误响应示例
```json
{
  "code": 1001,
  "message": "对账单号不存在",
  "data": null,
  "timestamp": "2024-12-01 10:30:00"
}
```

## 5. 注意事项

1. **权限控制**: 所有接口都需要进行权限验证，确保用户有相应的操作权限
2. **数据一致性**: 对账单确认后，相关数据将锁定，不允许修改
3. **金额精度**: 所有金额字段使用BigDecimal类型，保留2位小数
4. **时间格式**: 所有时间字段使用ISO 8601格式：YYYY-MM-DD HH:mm:ss
5. **分页查询**: 分页查询接口支持排序，默认按创建时间倒序排列
6. **文件导出**: 导出接口返回文件流，需要设置正确的Content-Type
7. **日志记录**: 所有关键操作都需要记录操作日志，包括操作人、操作时间、操作内容等 