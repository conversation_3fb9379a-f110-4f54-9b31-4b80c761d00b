<!--
  页面名称：售后记录标签页
  功能描述：展示家政服务订单的售后记录
-->
<template>
  <div class="after-sales-tab">
    <div class="section-header">
      <div class="section-title">
        <el-icon><Tools /></el-icon>
        售后记录
      </div>
      <div class="header-actions">
        <el-button type="primary" size="small" @click="handleAddAfterSales">
          <el-icon><Plus /></el-icon>
          新建售后工单
        </el-button>
      </div>
    </div>

    <div class="after-sales-content">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      <div v-else-if="afterSalesRecords.length > 0" class="records-table">
        <el-table :data="afterSalesRecords" style="width: 100%" size="small">
          <el-table-column prop="workOrderType" label="工单类型" width="120" />
          <el-table-column prop="problemDescription" label="问题描述" min-width="200" />
          <el-table-column prop="workOrderStatus" label="工单状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.workOrderStatus)" size="small">
                {{ getStatusText(scope.row.workOrderStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="processingResult" label="处理结果" min-width="200" />
          <el-table-column prop="createTime" label="创建时间" width="150" />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleEditRecord(scope.row)">
                编辑
              </el-button>
              <el-button type="danger" size="small" @click="handleDeleteRecord(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div v-else class="no-records">
        <el-empty description="暂无售后记录" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Tools, Plus } from '@element-plus/icons-vue'
import { HousekeepingServiceOrderApi } from '@/api/OrderCenter/HousekeepingServiceOrder'

interface Props {
  orderDetail: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'add-after-sales': []
  'edit-record': [record: any]
}>()

// 响应式数据
const loading = ref(false)
const afterSalesRecords = ref<any[]>([])

// 获取售后记录
const fetchAfterSalesRecords = async () => {
  if (!props.orderDetail?.id) return

  loading.value = true
  try {
    const result = await HousekeepingServiceOrderApi.getAfterSalesList(props.orderDetail.id)
    afterSalesRecords.value = result.records || []
    console.log('售后记录获取成功:', result)
  } catch (error) {
    console.error('获取售后记录失败:', error)
    ElMessage.error('获取售后记录失败')
    afterSalesRecords.value = []
  } finally {
    loading.value = false
  }
}

// 监听订单详情变化
watch(
  () => props.orderDetail,
  (newVal) => {
    if (newVal?.id) {
      fetchAfterSalesRecords()
    }
  },
  { immediate: true }
)

// 添加售后工单
const handleAddAfterSales = () => {
  emit('add-after-sales')
}

// 编辑记录
const handleEditRecord = (record: any) => {
  emit('edit-record', record)
}

// 删除记录
const handleDeleteRecord = async (record: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这条售后记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await HousekeepingServiceOrderApi.deleteAfterSalesRecord(record.id)
    ElMessage.success('删除成功')
    fetchAfterSalesRecords() // 刷新列表
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除售后记录失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 刷新售后记录数据
const refreshAfterSalesRecords = () => {
  fetchAfterSalesRecords()
}

// 暴露刷新方法给父组件
defineExpose({
  refreshAfterSalesRecords
})

// 获取状态类型
const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    processing: 'primary',
    completed: 'success',
    cancelled: 'info',
    待处理: 'warning',
    处理中: 'primary',
    已完成: 'success',
    已取消: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消',
    待处理: '待处理',
    处理中: '处理中',
    已完成: '已完成',
    已取消: '已取消'
  }
  return statusMap[status] || status
}

// 组件挂载时获取数据
// 移除重复的onMounted钩子，避免重复调用
// onMounted(() => {
//   if (props.orderDetail?.id) {
//     fetchAfterSalesList()
//   }
// })
</script>

<style scoped lang="scss">
.after-sales-tab {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .section-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      color: #303133;

      .el-icon {
        margin-right: 8px;
        color: #409eff;
      }
    }
  }

  .after-sales-content {
    .loading-container {
      padding: 20px;
    }

    .records-table {
      .el-table {
        .el-table__header {
          background-color: #f5f7fa;
        }
      }
    }

    .no-records {
      padding: 40px 0;
      text-align: center;
    }
  }
}
</style>
