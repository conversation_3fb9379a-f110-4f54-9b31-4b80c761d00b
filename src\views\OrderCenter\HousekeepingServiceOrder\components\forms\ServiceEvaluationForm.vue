<!--
  页面名称：服务评价表单
  功能描述：新增/编辑家政服务订单的服务评价
-->
<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="isEdit ? '编辑评价' : '订单评价'"
    direction="rtl"
    size="50%"
    :before-close="handleClose"
  >
    <div class="form-container">
      <div class="form-section">
        <h3 class="section-title">服务评分</h3>

        <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
          <el-form-item label="总体满意度" prop="overallRating" required>
            <el-rate v-model="form.overallRating" />
          </el-form-item>

          <el-form-item label="评价标签" prop="tags">
            <el-input
              v-model="form.tags"
              placeholder="多个请用英文逗号,隔开"
              type="textarea"
              :rows="2"
            />
            <div class="form-tip">多个标签请用英文逗号分隔，如：非常专业,准时到达,清洁彻底</div>
          </el-form-item>

          <el-form-item label="详细评价" prop="comment" required>
            <el-input
              v-model="form.comment"
              type="textarea"
              :rows="6"
              placeholder="请详细描述您的服务体验"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">提交评价</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { HousekeepingServiceOrderApi } from '@/api/OrderCenter/HousekeepingServiceOrder'

interface Props {
  visible: boolean
  orderId?: string
  orderNo?: string
  evaluationData?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderId: '',
  orderNo: '',
  evaluationData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [data?: any]
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()
const isEdit = ref(false)

// 表单数据
const form = reactive({
  overallRating: 5,
  tags: '',
  comment: ''
})

// 表单校验规则
const rules: FormRules = {
  overallRating: [{ required: true, message: '请选择总体满意度', trigger: 'change' }],
  comment: [{ required: true, message: '请输入详细评价', trigger: 'blur' }]
}

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    console.log('表单visible变化:', newVal, 'evaluationData:', props.evaluationData)
    if (newVal) {
      if (props.evaluationData) {
        // 编辑模式
        console.log('进入编辑模式，评价数据:', props.evaluationData)
        isEdit.value = true
        loadEvaluationData(props.evaluationData)
      } else {
        // 新增模式
        console.log('进入新增模式')
        isEdit.value = false
        resetForm()
      }
    }
  }
)

// 监听evaluationData变化
watch(
  () => props.evaluationData,
  (newVal) => {
    console.log('evaluationData变化:', newVal, 'visible:', props.visible)
    if (newVal && props.visible) {
      console.log('evaluationData变化，进入编辑模式')
      isEdit.value = true
      loadEvaluationData(newVal)
    }
  }
)

// 加载评价数据到表单
const loadEvaluationData = (data: any) => {
  console.log('加载评价数据到表单:', data)

  // 设置评分 - 支持多种字段名
  form.overallRating = data.overallRating || data.professionalRating || data.rating || 5

  // 设置标签 - 支持JSON字符串和数组格式
  if (typeof data.evaluationTags === 'string') {
    try {
      const tagsArray = JSON.parse(data.evaluationTags)
      form.tags = Array.isArray(tagsArray) ? tagsArray.join(',') : ''
    } catch (error) {
      console.error('解析评价标签JSON失败:', error)
      form.tags = ''
    }
  } else if (Array.isArray(data.evaluationTags)) {
    form.tags = data.evaluationTags.join(',')
  } else if (Array.isArray(data.tags)) {
    form.tags = data.tags.join(',')
  } else if (typeof data.tags === 'string') {
    form.tags = data.tags
  } else {
    form.tags = ''
  }

  // 设置评价内容 - 支持多种字段名
  form.comment = data.evaluationContent || data.comment || ''

  console.log('表单数据已加载:', form)
}

const resetForm = () => {
  form.overallRating = 5
  form.tags = ''
  form.comment = ''
  formRef.value?.clearValidate()
}

const handleClose = () => {
  emit('update:visible', false)
  resetForm()
  isEdit.value = false
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    console.log('表单提交 - orderId:', props.orderId)
    console.log('表单提交 - orderNo:', props.orderNo)
    console.log('表单提交 - evaluationData:', props.evaluationData)

    // 构建提交的数据
    const submitData = {
      orderId: props.orderId,
      orderNo: props.orderNo,
      overallRating: form.overallRating,
      evaluationTags: JSON.stringify(form.tags.split(',').filter((tag) => tag.trim())),
      evaluationContent: form.comment,
      evaluationTime: Date.now()
    }

    console.log('服务评价表单提交数据:', submitData)

    if (isEdit.value) {
      // 编辑模式 - 使用evaluationId
      if (props.evaluationData && props.evaluationData.evaluationId) {
        console.log('编辑模式 - evaluationId:', props.evaluationData.evaluationId)
        console.log('编辑模式 - orderId:', props.orderId)
        console.log('编辑模式 - orderNo:', props.orderNo)

        const updateData = {
          evaluationId: props.evaluationData.evaluationId.toString(),
          orderId: props.orderId, // 添加orderId参数
          overallRating: form.overallRating,
          evaluationTags: JSON.stringify(form.tags.split(',').filter((tag) => tag.trim())),
          evaluationContent: form.comment
        }

        console.log('编辑模式 - 提交数据:', updateData)

        await HousekeepingServiceOrderApi.updateServiceEvaluation(updateData as any)
        ElMessage.success('评价更新成功')
      } else {
        console.error('编辑模式 - 未找到evaluationId')
        ElMessage.error('未找到评价记录')
      }
    } else {
      // 新增模式
      console.log('新增模式 - orderId:', props.orderId)
      await HousekeepingServiceOrderApi.addServiceEvaluation(submitData)
      ElMessage.success('评价提交成功')
    }

    emit('success', submitData)
    handleClose()
  } catch (error) {
    console.error('表单提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.form-container {
  padding: 20px;

  .form-section {
    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e4e7ed;
    }

    .form-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }
  }
}

.drawer-footer {
  text-align: right;
}
</style>
