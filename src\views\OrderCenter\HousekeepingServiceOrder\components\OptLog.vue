<!--
  页面名称：家政服务订单操作日志
  功能描述：展示家政服务订单的操作日志
-->
<template>
  <el-drawer
    :model-value="visible"
    :title="`${orderData?.orderNumber || ''} - 操作日志`"
    direction="rtl"
    size="700px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @update:model-value="handleUpdateVisible"
  >
    <div class="optlog-container">
      <!-- 搜索筛选 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" @submit.prevent="handleSearch">
          <el-form-item label="日志类型">
            <el-select v-model="searchForm.logType" placeholder="请选择日志类型" clearable>
              <el-option label="全部" value="" />
              <el-option label="订单创建" value="订单创建" />
              <el-option label="订单编辑" value="订单编辑" />
              <el-option label="审批通过" value="审批通过" />
              <el-option label="审批驳回" value="审批驳回" />
              <el-option label="确认收款" value="确认收款" />
              <el-option label="订单完成" value="订单完成" />
              <el-option label="系统操作" value="系统管理员" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 日志内容 -->
      <div v-else class="log-content">
        <div v-if="filteredLogList.length === 0" class="no-logs">
          <el-empty description="暂无操作日志" />
        </div>
        <div v-else class="log-timeline">
          <el-timeline>
            <el-timeline-item
              v-for="(log, index) in filteredLogList"
              :key="log.id || index"
              :timestamp="formatDateTime(log.createTime)"
              placement="top"
              :color="getLogColor(log.logType)"
            >
              <div class="log-item">
                <div class="log-header">
                  <span class="log-user"
                    >{{ log.operatorName || '系统' }} ({{ log.operatorRole || '管理员' }})</span
                  >
                  <el-button
                    :type="getButtonType(log.logType) as any"
                    size="small"
                    class="log-button"
                  >
                    {{ getButtonText(log.logType) }}
                  </el-button>
                </div>
                <div class="log-content-text">
                  <div class="log-description">{{ log.logTitle || '操作记录' }}</div>
                  <div
                    v-if="
                      parsedLogs[log.id] &&
                      parsedLogs[log.id].details &&
                      parsedLogs[log.id].details.length > 0
                    "
                    class="log-details"
                  >
                    <div
                      v-for="(detail, detailIndex) in parsedLogs[log.id].details"
                      :key="detailIndex"
                      class="detail-item"
                    >
                      {{ detail }}
                    </div>
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { HousekeepingServiceOrderApi } from '@/api/OrderCenter/HousekeepingServiceOrder'

// Props
interface Props {
  visible: boolean
  orderData?: any
}

const props = withDefaults(defineProps<Props>(), {
  orderData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const loading = ref(false)
const logList = ref<any[]>([])
const parsedLogs = ref<Record<string, any>>({})

// 搜索表单
const searchForm = reactive({
  logType: '',
  dateRange: [] as string[]
})

// 计算属性
const filteredLogList = computed(() => {
  let filtered = logList.value

  // 按日志类型筛选
  if (searchForm.logType) {
    filtered = filtered.filter((log) => log.logType === searchForm.logType)
  }

  // 按时间范围筛选
  if (searchForm.dateRange && searchForm.dateRange.length === 2) {
    const [startDate, endDate] = searchForm.dateRange
    filtered = filtered.filter((log) => {
      const logDate = log.createTime?.split(' ')[0]
      return logDate >= startDate && logDate <= endDate
    })
  }

  return filtered
})

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal && props.orderData?.orderNumber) {
      fetchLogList()
    }
  },
  { immediate: true }
)

// 方法定义
const handleUpdateVisible = (value: boolean) => {
  emit('update:visible', value)
}

// 获取操作日志列表
const fetchLogList = async () => {
  if (!props.orderData?.orderNumber) return

  try {
    loading.value = true

    const result = await HousekeepingServiceOrderApi.getOperationLogList({
      orderNo: props.orderData.orderNumber,
      logType: searchForm.logType || undefined,
      startDate: searchForm.dateRange?.[0] || undefined,
      endDate: searchForm.dateRange?.[1] || undefined,
      page: 1,
      size: 100
    })

    // 修复数据结构：直接使用 result.list 而不是 result.data?.list
    logList.value = result.list || []

    // 解析日志内容
    logList.value.forEach((log) => {
      parsedLogs.value[log.id] = parseLogContent(log)
    })
  } catch (error) {
    console.error('获取操作日志失败:', error)
    ElMessage.error('获取操作日志失败')
  } finally {
    loading.value = false
  }
}

// 解析日志内容
const parseLogContent = (log: any) => {
  try {
    // 新增操作不展示JSON内容备注
    if (log.logType === '订单创建') {
      return {
        summary: '创建了新的家政服务订单',
        details: []
      }
    }

    // 其他操作需要展示JSON内容
    if (log.logContent) {
      const content = JSON.parse(log.logContent)

      if (log.logType === '订单编辑' && content.changes) {
        const changes = content.changes
        const details = Object.keys(changes).map((key) => {
          const change = changes[key]
          const fieldName = getFieldDisplayName(key)
          return `${fieldName}: ${change.old || '空'} → ${change.new || '空'}`
        })

        return {
          summary: '更新了订单信息',
          details: details
        }
      } else if (log.logType === '新增收款') {
        return {
          summary: `新增收款信息 - 金额: ¥${content.paymentAmount || '0'}, 方式: ${content.paymentType || '未知'}`,
          details: [
            `收款金额: ¥${content.paymentAmount || '0'}`,
            `收款方式: ${content.paymentType || '未知'}`,
            `收款时间: ${content.paymentTime || '未知'}`,
            `收款备注: ${content.paymentRemark || '无'}`
          ]
        }
      } else {
        return {
          summary: log.logTitle || '操作记录',
          details: [JSON.stringify(content, null, 2)]
        }
      }
    }

    return {
      summary: log.logTitle || '操作记录',
      details: []
    }
  } catch (error) {
    console.error('解析日志内容失败:', error)
    return {
      summary: log.logTitle || '操作记录',
      details: [log.logContent || '无法解析的内容']
    }
  }
}

// 获取字段显示名称
const getFieldDisplayName = (fieldName: string): string => {
  const fieldMap: Record<string, string> = {
    customerName: '客户姓名',
    customerPhone: '客户电话',
    serviceAddress: '服务地址',
    serviceType: '服务类型',
    servicePackage: '服务套餐',
    serviceStartDate: '服务开始日期',
    serviceEndDate: '服务结束日期',
    unitPrice: '单价',
    totalAmount: '总金额',
    actualAmount: '实际金额',
    practitionerOneid: '服务人员',
    agencyId: '服务机构',
    paymentMethod: '支付方式',
    paymentStatus: '支付状态',
    receivedAmount: '收款金额',
    paymentTime: '收款时间',
    paymentRemark: '收款备注'
  }
  return fieldMap[fieldName] || fieldName
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消',
    unpaid: '未支付',
    paid: '已支付',
    partial: '部分支付'
  }
  return statusMap[status] || status
}

// 获取日志图标类型
const getLogTypeIcon = (logType: string) => {
  const iconMap: Record<string, string> = {
    订单创建: 'Document',
    订单编辑: 'Edit',
    审批通过: 'Check',
    审批驳回: 'Close',
    确认收款: 'Money',
    订单完成: 'Check',
    系统管理员: 'Setting'
  }
  return iconMap[logType] || 'InfoFilled'
}

// 获取日志颜色
const getLogColor = (logType: string) => {
  const colorMap: Record<string, string> = {
    订单创建: '#67c23a',
    订单编辑: '#e6a23c',
    审批通过: '#67c23a',
    审批驳回: '#f56c6c',
    确认收款: '#409eff',
    订单完成: '#67c23a',
    系统管理员: '#909399'
  }
  return colorMap[logType] || '#909399'
}

// 获取按钮类型
const getButtonType = (logType: string) => {
  const typeMap: Record<string, string> = {
    订单创建: 'success',
    订单编辑: 'warning',
    审批通过: 'success',
    审批驳回: 'danger',
    确认收款: 'primary',
    订单完成: 'success',
    系统管理员: 'info'
  }
  return typeMap[logType] || 'info'
}

// 获取按钮文本
const getButtonText = (logType: string) => {
  return logType || '其他'
}

// 获取日志类型标签样式
const getLogTypeTag = (type: string): string => {
  const tagMap: Record<string, string> = {
    订单创建: 'success',
    订单编辑: 'warning',
    审批通过: 'success',
    审批驳回: 'danger',
    确认收款: 'primary',
    订单完成: 'success',
    系统管理员: 'info'
  }
  return tagMap[type] || 'info'
}

// 格式化日期时间
const formatDateTime = (timestamp: string | number) => {
  if (!timestamp) return '-'
  try {
    const date = new Date(timestamp)
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return '-'
  }
}

// 搜索处理
const handleSearch = () => {
  fetchLogList()
}

// 重置搜索
const handleReset = () => {
  searchForm.logType = ''
  searchForm.dateRange = null
  fetchLogList()
}
</script>

<style scoped lang="scss">
.optlog-container {
  padding: 20px;
  height: calc(100vh - 120px);
  overflow-y: auto;

  .search-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;

    .el-form {
      margin-bottom: 0;
    }
  }

  .loading-container {
    padding: 40px 0;
  }

  .log-content {
    .no-logs {
      padding: 60px 0;
      text-align: center;
    }

    .log-timeline {
      .log-item {
        background-color: #fff;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .log-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .log-user {
            font-weight: 500;
            color: #333;
            margin-right: 15px;
          }

          .log-button {
            flex-shrink: 0;
          }
        }

        .log-content-text {
          .log-description {
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
          }

          .log-details {
            margin-top: 10px;
            padding-left: 20px;
            border-left: 2px solid #e9e9eb;

            .detail-item {
              font-size: 14px;
              color: #666;
              margin-bottom: 5px;
              line-height: 1.5;
            }
          }
        }
      }
    }
  }
}

:deep(.el-timeline-item__node) {
  background-color: #409eff;
}

:deep(.el-timeline-item__timestamp) {
  color: #909399;
  font-size: 12px;
}
</style>
