/**
 * 日志解析器工具
 * 用于解析操作日志中的变更内容，提取字段变更信息
 */

export interface LogChangeItem {
  field: string
  oldValue: string
  newValue: string
  hasChanged: boolean
}

export interface ParsedLogContent {
  changes: LogChangeItem[]
  hasChanges: boolean
  rawContent: string
}

/**
 * 解析日志内容中的变更信息
 * @param logContent 日志内容字符串
 * @returns 解析后的变更信息
 */
export function parseLogContent(logContent: string): ParsedLogContent {
  if (!logContent) {
    return {
      changes: [],
      hasChanges: false,
      rawContent: logContent
    }
  }

  const changes: LogChangeItem[] = []

  // 首先尝试解析JSON格式的日志内容
  try {
    const jsonContent = JSON.parse(logContent)

    // 处理包含changes字段的JSON格式
    if (jsonContent.changes && Array.isArray(jsonContent.changes)) {
      jsonContent.changes.forEach((change: any) => {
        if (change.field && change.oldValue !== change.newValue) {
          changes.push({
            field: change.field,
            oldValue: String(change.oldValue || ''),
            newValue: String(change.newValue || ''),
            hasChanged: true
          })
        }
      })
    }

    // 处理包含operation字段的JSON格式（新增收支记录等）
    if (jsonContent.operation && jsonContent.operation === '新增收支记录') {
      changes.push({
        field: 'paymentAmount',
        oldValue: '-',
        newValue: jsonContent.paymentAmount || '',
        hasChanged: true
      })
      if (jsonContent.paymentRemark) {
        changes.push({
          field: 'paymentRemark',
          oldValue: '-',
          newValue: jsonContent.paymentRemark,
          hasChanged: true
        })
      }
      if (jsonContent.paymentType) {
        changes.push({
          field: 'paymentType',
          oldValue: '-',
          newValue: jsonContent.paymentType,
          hasChanged: true
        })
      }
    }

    // 如果JSON解析成功且有变更，直接返回
    if (changes.length > 0) {
      return {
        changes,
        hasChanges: true,
        rawContent: logContent
      }
    }
  } catch (error) {
    // JSON解析失败，继续尝试其他格式
  }

  // 常见的变更格式模式
  const patterns = [
    // 格式1: 字段名: 旧值 → 新值
    /(\w+):\s*([^→]+)→\s*([^\n]+)/g,
    // 格式2: 字段名从"旧值"变更为"新值"
    /(\w+)从"([^"]*)"变更为"([^"]*)"/g,
    // 格式3: 字段名: 旧值 -> 新值
    /(\w+):\s*([^->]+)->\s*([^\n]+)/g,
    // 格式4: 字段名由"旧值"改为"新值"
    /(\w+)由"([^"]*)"改为"([^"]*)"/g,
    // 格式5: 字段名 旧值 改为 新值
    /(\w+)\s+([^\s]+)\s+改为\s+([^\s]+)/g,
    // 格式6: 字段名从旧值更新为新值
    /(\w+)从([^\s]+)更新为([^\s]+)/g
  ]

  patterns.forEach((pattern) => {
    let match
    while ((match = pattern.exec(logContent)) !== null) {
      const field = match[1].trim()
      const oldValue = match[2].trim()
      const newValue = match[3].trim()

      // 检查值是否真的发生了变化
      const hasChanged =
        oldValue !== newValue &&
        oldValue !== '' &&
        newValue !== '' &&
        oldValue !== 'null' &&
        newValue !== 'null' &&
        oldValue !== 'undefined' &&
        newValue !== 'undefined' &&
        oldValue !== '空' &&
        newValue !== '空'

      if (hasChanged) {
        changes.push({
          field,
          oldValue,
          newValue,
          hasChanged: true
        })
      }
    }
  })

  // 如果没有找到标准格式，尝试解析特殊格式
  if (changes.length === 0) {
    // 尝试解析"新增收支记录"格式
    const paymentMatch = logContent.match(/新增收支记录信息.*?paymentAmount["\s]*:["\s]*([^,\s}]+)/)
    if (paymentMatch) {
      changes.push({
        field: 'paymentAmount',
        oldValue: '-',
        newValue: paymentMatch[1],
        hasChanged: true
      })
    }

    // 尝试解析"订单信息更新"格式
    const orderUpdateMatch = logContent.match(
      /订单信息更新.*?单价["\s]*:["\s]*([^→]+)→["\s]*([^\s]+)/
    )
    if (orderUpdateMatch) {
      changes.push({
        field: 'unitPrice',
        oldValue: orderUpdateMatch[1].trim(),
        newValue: orderUpdateMatch[2].trim(),
        hasChanged: true
      })
    }
  }

  return {
    changes,
    hasChanges: changes.length > 0,
    rawContent: logContent
  }
}

/**
 * 通用操作日志解析器
 * 支持多种订单类型的日志内容解析
 * @param logContent 日志内容
 * @param orderType 订单类型（可选）
 * @returns 解析结果
 */
export function parseOperationLog(logContent: string, orderType?: string): ParsedLogContent {
  if (!logContent) {
    return {
      changes: [],
      hasChanges: false,
      rawContent: logContent
    }
  }

  const changes: LogChangeItem[] = []

  // 1. 首先尝试解析JSON格式
  try {
    const jsonContent = JSON.parse(logContent)

    // 处理新增收支记录格式
    if (jsonContent.operation === '新增收支记录' || jsonContent.operation === '新增收支记录信息') {
      if (jsonContent.paymentAmount) {
        changes.push({
          field: 'paymentAmount',
          oldValue: '-',
          newValue: String(jsonContent.paymentAmount),
          hasChanged: true
        })
      }
      if (jsonContent.paymentRemark) {
        changes.push({
          field: 'paymentRemark',
          oldValue: '-',
          newValue: String(jsonContent.paymentRemark),
          hasChanged: true
        })
      }
      if (jsonContent.paymentType) {
        changes.push({
          field: 'paymentType',
          oldValue: '-',
          newValue: String(jsonContent.paymentType),
          hasChanged: true
        })
      }
      if (jsonContent.paymentTime) {
        changes.push({
          field: 'paymentTime',
          oldValue: '-',
          newValue: String(jsonContent.paymentTime),
          hasChanged: true
        })
      }
    }

    // 处理包含changes字段的JSON格式
    if (jsonContent.changes && Array.isArray(jsonContent.changes)) {
      jsonContent.changes.forEach((change: any) => {
        if (change.field && change.oldValue !== change.newValue) {
          changes.push({
            field: change.field,
            oldValue: String(change.oldValue || ''),
            newValue: String(change.newValue || ''),
            hasChanged: true
          })
        }
      })
    }

    // 处理对象格式的变更
    if (jsonContent.changes && typeof jsonContent.changes === 'object') {
      Object.keys(jsonContent.changes).forEach((key) => {
        const change = jsonContent.changes[key]
        if (change && change.old !== change.new) {
          changes.push({
            field: key,
            oldValue: String(change.old || ''),
            newValue: String(change.new || ''),
            hasChanged: true
          })
        }
      })
    }

    // 如果JSON解析成功且有变更，直接返回
    if (changes.length > 0) {
      return {
        changes,
        hasChanges: true,
        rawContent: logContent
      }
    }
  } catch (error) {
    // JSON解析失败，继续尝试其他格式
  }

  // 2. 尝试解析文本格式的变更
  const textPatterns = [
    // 匹配 "单价: 599.00 → 空" 格式
    /单价:\s*([^→]+)→\s*([^\n]+)/g,
    /总金额:\s*([^→]+)→\s*([^\n]+)/g,
    /实际金额:\s*([^→]+)→\s*([^\n]+)/g,
    // 匹配其他字段变更
    /(\w+):\s*([^→]+)→\s*([^\n]+)/g,
    // 匹配 "字段名 旧值 改为 新值" 格式
    /(\w+)\s+([^\s]+)\s+改为\s+([^\s]+)/g,
    // 匹配 "字段名从旧值更新为新值" 格式
    /(\w+)从([^\s]+)更新为([^\s]+)/g
  ]

  textPatterns.forEach((pattern) => {
    let match
    while ((match = pattern.exec(logContent)) !== null) {
      const field = match[1]?.trim() || ''
      const oldValue = match[2]?.trim() || '空'
      const newValue = match[3]?.trim() || '空'

      if (oldValue !== newValue && field) {
        changes.push({
          field,
          oldValue,
          newValue,
          hasChanged: true
        })
      }
    }
  })

  // 3. 尝试解析特殊格式
  if (changes.length === 0) {
    // 尝试解析"新增收支记录"格式
    const paymentMatch = logContent.match(/新增收支记录信息.*?paymentAmount["\s]*:["\s]*([^,\s}]+)/)
    if (paymentMatch) {
      changes.push({
        field: 'paymentAmount',
        oldValue: '-',
        newValue: paymentMatch[1],
        hasChanged: true
      })
    }

    // 尝试解析"订单信息更新"格式
    const orderUpdateMatch = logContent.match(
      /订单信息更新.*?单价["\s]*:["\s]*([^→]+)→["\s]*([^\s]+)/
    )
    if (orderUpdateMatch) {
      changes.push({
        field: 'unitPrice',
        oldValue: orderUpdateMatch[1].trim(),
        newValue: orderUpdateMatch[2].trim(),
        hasChanged: true
      })
    }
  }

  return {
    changes,
    hasChanges: changes.length > 0,
    rawContent: logContent
  }
}

/**
 * 获取操作日志的摘要信息
 * @param logType 日志类型
 * @param changes 变更列表
 * @returns 摘要信息
 */
export function getLogSummary(logType: string, changes: LogChangeItem[]): string {
  if (logType === '新增收支记录' || logType === '新增收支记录信息') {
    return '新增收支记录'
  }

  if (changes.length > 0) {
    return '更新了订单信息'
  }

  return '操作记录'
}

/**
 * 格式化字段名称显示
 * @param field 字段名
 * @returns 格式化后的字段名
 */
export function formatFieldName(field: string): string {
  const fieldMap: Record<string, string> = {
    // 订单基本信息
    orderNo: '订单号',
    orderNumber: '订单号',
    orderId: '订单ID',
    orderStatus: '订单状态',
    paymentStatus: '支付状态',
    createTime: '创建时间',
    updateTime: '更新时间',

    // 客户信息
    customerName: '客户姓名',
    customerPhone: '客户电话',
    customerAddress: '客户地址',
    serviceAddress: '服务地址',

    // 服务信息
    serviceType: '服务类型',
    servicePackage: '服务套餐',
    serviceStartDate: '服务开始日期',
    serviceEndDate: '服务结束日期',
    serviceDuration: '服务时长',
    serviceFrequency: '服务频率',
    serviceDescription: '服务描述',

    // 金额信息
    unitPrice: '单价',
    totalAmount: '总金额',
    actualAmount: '实际金额',
    discountAmount: '优惠金额',
    receivedAmount: '收款金额',
    paymentAmount: '支付金额',

    // 人员信息
    practitionerOneid: '服务人员',
    practitionerId: '服务人员ID',
    agencyId: '服务机构',
    agencyName: '服务机构名称',

    // 支付信息
    paymentMethod: '支付方式',
    paymentTime: '支付时间',
    paymentRemark: '支付备注',
    paymentType: '支付类型',
    transactionId: '交易ID',

    // 评价信息
    overallRating: '总体评分',
    tags: '评价标签',
    comment: '评价内容',
    evaluationTime: '评价时间',

    // 商机线索
    opportunityId: '商机ID',
    leadId: '线索ID',
    businessOpportunity: '商机',
    lead: '线索',

    // 其他字段
    remark: '备注',
    description: '描述',
    title: '标题',
    content: '内容',
    status: '状态',
    type: '类型',
    date: '日期',
    time: '时间',
    amount: '金额',
    price: '价格',
    name: '名称',
    phone: '电话',
    address: '地址',
    email: '邮箱',
    id: 'ID',
    code: '编码',
    number: '编号'
  }

  return fieldMap[field] || field
}

/**
 * 格式化字段值显示
 * @param value 字段值
 * @param field 字段名
 * @returns 格式化后的字段值
 */
export function formatFieldValue(value: string, field: string): string {
  if (!value || value === 'null' || value === 'undefined') {
    return '-'
  }

  // 特殊字段的格式化
  switch (field) {
    case 'totalAmount':
    case 'collectionAmount':
      return `¥${Number(value).toFixed(2)}`
    case 'startDate':
    case 'endDate':
    case 'collectionDate':
      return formatDate(value)
    case 'paymentStatus':
      return formatPaymentStatus(value)
    case 'orderStatus':
      return formatOrderStatus(value)
    case 'contractType':
      return formatContractType(value)
    default:
      return value
  }
}

/**
 * 格式化日期
 * @param dateString 日期字符串
 * @returns 格式化后的日期
 */
function formatDate(dateString: string): string {
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN')
  } catch {
    return dateString
  }
}

/**
 * 格式化支付状态
 * @param status 状态值
 * @returns 格式化后的状态
 */
function formatPaymentStatus(status: string): string {
  const statusMap: Record<string, string> = {
    pending: '待支付',
    paid: '已支付',
    refunded: '已退款',
    partial: '部分支付'
  }
  return statusMap[status] || status
}

/**
 * 格式化订单状态
 * @param status 状态值
 * @returns 格式化后的状态
 */
function formatOrderStatus(status: string): string {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    pending_approval: '待审批',
    approving: '审批中',
    approved: '已批准',
    rejected: '已拒绝',
    executing: '执行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

/**
 * 格式化合同类型
 * @param type 类型值
 * @returns 格式化后的类型
 */
function formatContractType(type: string): string {
  const typeMap: Record<string, string> = {
    electronic: '电子合同',
    paper: '纸质合同',
    both: '电子+纸质'
  }
  return typeMap[type] || type
}
