# 家政服务订单API接口文档

## 1. 接口概述

### 1.1 基础信息

- **基础路径**: `/publicbiz/domestic-task-order`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: Bearer Token

### 1.2 统一响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 1.3 错误码说明

| 错误码 | 说明           |
| ------ | -------------- |
| 200    | 操作成功       |
| 400    | 请求参数错误   |
| 401    | 未授权访问     |
| 403    | 权限不足       |
| 404    | 资源不存在     |
| 500    | 服务器内部错误 |

## 2. 订单管理接口

### 2.1 分页查询订单列表

**接口地址**: `GET /publicbiz/domestic-task-orderpage`

**功能说明**: 分页查询家政服务订单列表，支持多条件筛选

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | page | Integer | 是 | 页码，从1开始 | | size | Integer | 是 | 每页大小，最大100 | | orderStatus | String | 否 | 订单状态：pending-待派单/assigned-已派单/in_service-服务中/completed-已完成/cancelled-已取消 | | paymentStatus | String | 否 | 支付状态：unpaid-未支付/paid-已支付/partial-部分支付 | | serviceType | String | 否 | 服务类型：maternity-月嫂服务/deep_cleaning-深度保洁/hourly-小时工/nanny-育儿嫂服务 | | keyword | String | 否 | 搜索关键词（客户姓名、服务人员、服务机构） | | startDate | String | 否 | 开始日期，格式：YYYY-MM-DD | | endDate | String | 否 | 结束日期，格式：YYYY-MM-DD |

**响应字段**: | 字段名 | 类型 | 说明 | |--------|------|------| | total | Integer | 总记录数 | | pages | Integer | 总页数 | | current | Integer | 当前页码 | | size | Integer | 每页大小 | | records | Array | 订单列表 | | records[].id | String | 订单ID | | records[].orderNumber | String | 订单编号 | | records[].customerName | String | 客户姓名 | | records[].customerPhone | String | 客户电话 | | records[].serviceType | String | 服务类型 | | records[].servicePersonnel | String | 服务人员 | | records[].serviceAgency | String | 服务机构 | | records[].serviceAmount | Decimal | 服务金额 | | records[].completedTasks | Integer | 已完成任务数 | | records[].totalTasks | Integer | 总任务数 | | records[].orderStatus | String | 订单状态 | | records[].paymentStatus | String | 支付状态 | | records[].appointmentTime | String | 预约时间 |

**请求示例**:

```bash
GET /publicbiz/domestic-task-orderpage?page=1&size=10&orderStatus=in_service&keyword=李女士
```

**响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 18,
    "pages": 2,
    "current": 1,
    "size": 10,
    "records": [
      {
        "id": "1",
        "orderNumber": "DS202406001",
        "customerName": "李女士",
        "customerPhone": "13812345678",
        "serviceType": "月嫂服务",
        "servicePersonnel": "张阿姨",
        "serviceAgency": "爱家月嫂服务中心",
        "serviceAmount": 12800.0,
        "completedTasks": 15,
        "totalTasks": 30,
        "orderStatus": "in_service",
        "paymentStatus": "paid",
        "appointmentTime": "2024-06-01 09:00:00"
      }
    ]
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 2.2 新增订单

**接口地址**: `POST /publicbiz/domestic-task-orderadd`

**功能说明**: 新增家政服务订单

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | businessOpportunity | String | 否 | 关联商机ID | | lead | String | 否 | 关联线索ID | | customerName | String | 是 | 客户姓名 | | customerPhone | String | 是 | 客户电话 | | serviceAddress | String | 是 | 服务地址 | | servicePackage | String | 是 | 服务套餐ID | | serviceType | String | 是 | 服务类型 | | serviceStartDate | String | 是 | 服务开始日期 | | serviceEndDate | String | 否 | 服务结束日期 | | serviceDuration | String | 否 | 服务时长 | | serviceFrequency | String | 否 | 服务频次 | | unitPrice | Decimal | 是 | 单价 | | totalAmount | Decimal | 是 | 订单总金额 | | discountAmount | Decimal | 否 | 优惠金额 | | actualAmount | Decimal | 是 | 实付金额 | | practitionerOneid | String | 否 | 服务人员OneID | | agencyId | Long | 否 | 服务机构ID | | remark | String | 否 | 备注 |

**响应字段**: | 字段名 | 类型 | 说明 | |--------|------|------| | orderId | String | 订单ID | | orderNumber | String | 订单编号 |

**请求示例**:

```json
{
  "customerName": "王女士",
  "customerPhone": "13987654321",
  "serviceAddress": "北京市朝阳区建国路88号",
  "servicePackage": "package_a",
  "serviceType": "maternity",
  "serviceStartDate": "2024-07-01",
  "serviceEndDate": "2024-07-31",
  "serviceDuration": "30天",
  "unitPrice": 12800.0,
  "totalAmount": 12800.0,
  "actualAmount": 12800.0,
  "practitionerOneid": "practitioner_001",
  "agencyId": 1001,
  "remark": "客户要求月嫂有3年以上经验"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "订单创建成功",
  "data": {
    "orderId": "1001",
    "orderNumber": "DS202406005"
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 2.3 更新订单

**接口地址**: `POST /publicbiz/domestic-task-orderupdate`

**功能说明**: 更新家政服务订单信息

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | id | String | 是 | 订单ID | | customerName | String | 否 | 客户姓名 | | customerPhone | String | 否 | 客户电话 | | serviceAddress | String | 否 | 服务地址 | | serviceStartDate | String | 否 | 服务开始日期 | | serviceEndDate | String | 否 | 服务结束日期 | | practitionerOneid | String | 否 | 服务人员OneID | | agencyId | Long | 否 | 服务机构ID | | remark | String | 否 | 备注 |

**响应示例**:

```json
{
  "code": 200,
  "message": "订单更新成功",
  "data": null,
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 2.4 删除订单

**接口地址**: `POST /publicbiz/domestic-task-orderdelete`

**功能说明**: 删除家政服务订单

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | id | String | 是 | 订单ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "订单删除成功",
  "data": null,
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 2.5 获取订单详情

**接口地址**: `GET /publicbiz/domestic-task-orderdetail`

**功能说明**: 获取家政服务订单详细信息

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | id | String | 是 | 订单ID |

**响应字段**: | 字段名 | 类型 | 说明 | |--------|------|------| | orderInfo | Object | 订单基本信息 | | customerInfo | Object | 客户信息 | | serviceInfo | Object | 服务信息 | | paymentInfo | Object | 支付信息 | | taskInfo | Object | 任务信息 |

**响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "orderInfo": {
      "id": "1",
      "orderNumber": "DS202406001",
      "orderStatus": "in_service",
      "paymentStatus": "paid",
      "createTime": "2024-06-01 09:00:00",
      "updateTime": "2024-06-15 10:30:00"
    },
    "customerInfo": {
      "customerName": "李女士",
      "customerPhone": "13812345678",
      "serviceAddress": "北京市朝阳区建国路88号"
    },
    "serviceInfo": {
      "serviceType": "月嫂服务",
      "servicePackage": "SP001 - 月嫂服务套餐",
      "serviceStartDate": "2024-06-01",
      "serviceEndDate": "2024-06-30",
      "serviceDuration": "30天",
      "serviceAmount": 12800.0
    },
    "paymentInfo": {
      "totalAmount": 12800.0,
      "paidAmount": 12800.0,
      "paymentMethod": "银行转账",
      "paymentTime": "2024-06-01 10:00:00"
    },
    "taskInfo": {
      "totalTasks": 30,
      "completedTasks": 15,
      "taskProgress": 50.0
    }
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 2.6 导出订单列表

**接口地址**: `GET /publicbiz/domestic-task-orderexport`

**功能说明**: 导出家政服务订单列表

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | orderStatus | String | 否 | 订单状态 | | paymentStatus | String | 否 | 支付状态 | | serviceType | String | 否 | 服务类型 | | keyword | String | 否 | 搜索关键词 | | startDate | String | 否 | 开始日期 | | endDate | String | 否 | 结束日期 | | format | String | 否 | 导出格式：excel/csv，默认excel |

**响应**: 文件流

## 3. 服务任务管理接口

### 3.1 分页查询任务列表

**接口地址**: `GET /publicbiz/domestic-task-ordertask/page`

**功能说明**: 分页查询服务任务列表

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | orderId | String | 是 | 订单ID | | page | Integer | 是 | 页码 | | size | Integer | 是 | 每页大小 | | taskStatus | String | 否 | 任务状态：pending-待指派/assigned-已分配/in_progress-进行中/completed-已完成/cancelled-已取消 | | executor | String | 否 | 执行人员姓名 | | dateRange | Array | 否 | 日期范围 |

**响应字段**: | 字段名 | 类型 | 说明 | |--------|------|------| | total | Integer | 总记录数 | | records | Array | 任务列表 | | records[].taskId | String | 任务ID | | records[].taskSequence | String | 任务序号 | | records[].plannedDate | String | 计划服务日期 | | records[].plannedContent | String | 计划服务内容 | | records[].taskStatus | String | 任务状态 | | records[].currentPersonnel | String | 当前服务人员 | | records[].finalPersonnel | String | 最终完成人员 | | records[].completionTime | String | 完成时间 | | records[].punchLocation | String | 打卡地点 | | records[].completionCertificate | String | 完成凭证URL |

**响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 30,
    "records": [
      {
        "taskId": "task_001",
        "taskSequence": "第1次服务",
        "plannedDate": "2024-06-01",
        "plannedContent": "新生儿护理、产妇护理、月子餐制作",
        "taskStatus": "completed",
        "currentPersonnel": "张阿姨",
        "finalPersonnel": "张阿姨",
        "completionTime": "2024-06-01 18:00:00",
        "punchLocation": "北京市朝阳区建国路88号",
        "completionCertificate": "https://example.com/certificate1.jpg"
      }
    ]
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 3.2 完成任务

**接口地址**: `POST /publicbiz/domestic-task-ordertask/complete`

**功能说明**: 完成任务并上传完成凭证

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | taskId | String | 是 | 任务ID | | finalPersonnel | String | 是 | 最终完成人员 | | completionTime | String | 是 | 完成时间 | | punchLocation | String | 是 | 打卡地点 | | certificateFile | File | 是 | 完成凭证文件 | | remarks | String | 否 | 备注 |

**响应示例**:

```json
{
  "code": 200,
  "message": "任务完成成功",
  "data": null,
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 3.3 指派任务

**接口地址**: `POST /publicbiz/domestic-task-ordertask/assign`

**功能说明**: 指派任务给服务人员

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | taskId | String | 是 | 任务ID | | practitionerOneid | String | 是 | 服务人员OneID | | practitionerName | String | 是 | 服务人员姓名 | | practitionerPhone | String | 是 | 服务人员电话 | | plannedStartTime | String | 是 | 计划开始时间 | | plannedEndTime | String | 是 | 计划结束时间 | | remarks | String | 否 | 备注 |

**响应示例**:

```json
{
  "code": 200,
  "message": "任务指派成功",
  "data": null,
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 3.4 取消任务

**接口地址**: `POST /publicbiz/domestic-task-ordertask/cancel`

**功能说明**: 取消任务

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | taskId | String | 是 | 任务ID | | cancelReason | String | 是 | 取消原因 | | remarks | String | 否 | 备注 |

**响应示例**:

```json
{
  "code": 200,
  "message": "任务取消成功",
  "data": null,
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 3.5 编辑任务

**接口地址**: `POST /publicbiz/domestic-task-ordertask/edit`

**功能说明**: 编辑任务信息

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | taskId | String | 是 | 任务ID | | taskName | String | 否 | 任务名称 | | taskDescription | String | 否 | 任务描述 | | plannedStartTime | String | 否 | 计划开始时间 | | plannedEndTime | String | 否 | 计划结束时间 | | practitionerOneid | String | 否 | 服务人员OneID | | remarks | String | 否 | 备注 |

**响应示例**:

```json
{
  "code": 200,
  "message": "任务编辑成功",
  "data": null,
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 3.6 查看完成凭证

**接口地址**: `GET /publicbiz/domestic-task-ordertask/certificate`

**功能说明**: 查看任务完成凭证

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | taskId | String | 是 | 任务ID |

**响应字段**: | 字段名 | 类型 | 说明 | |--------|------|------| | certificateUrl | String | 凭证文件URL | | certificateType | String | 凭证类型 | | uploadTime | String | 上传时间 | | uploadPerson | String | 上传人员 |

**响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "certificateUrl": "https://example.com/certificate1.jpg",
    "certificateType": "image",
    "uploadTime": "2024-06-01 18:00:00",
    "uploadPerson": "张阿姨"
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 3.7 批量重指派

**接口地址**: `POST /publicbiz/domestic-task-ordertask/batch-reassign`

**功能说明**: 批量重指派任务

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | taskIds | Array | 是 | 任务ID列表 | | practitionerOneid | String | 是 | 服务人员OneID | | practitionerName | String | 是 | 服务人员姓名 | | practitionerPhone | String | 是 | 服务人员电话 | | reassignReason | String | 是 | 重指派原因 | | remarks | String | 否 | 备注 |

**响应示例**:

```json
{
  "code": 200,
  "message": "批量重指派成功",
  "data": {
    "successCount": 5,
    "failCount": 0
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

## 4. 收款信息管理接口

### 4.1 查询收款信息

**接口地址**: `GET /publicbiz/domestic-task-orderpayment/info`

**功能说明**: 查询订单收款信息

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | orderId | String | 是 | 订单ID |

**响应字段**: | 字段名 | 类型 | 说明 | |--------|------|------| | orderAmount | Decimal | 订单金额 | | paymentMethod | String | 收款方式 | | paymentNotes | String | 收款备注 | | paymentStatus | String | 支付状态 | | paymentDate | String | 收款日期 | | receivedAmount | Decimal | 收款金额 | | operator | String | 操作人 |

**响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "orderAmount": 12800.0,
    "paymentMethod": "银行转账",
    "paymentNotes": "客户通过银行转账支付,已确认到账",
    "paymentStatus": "paid",
    "paymentDate": "2024-05-30",
    "receivedAmount": 12800.0,
    "operator": "李小明(财务)"
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 4.2 新增收款信息

**接口地址**: `POST /publicbiz/domestic-task-orderpayment/add`

**功能说明**: 新增收款信息

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | orderId | String | 是 | 订单ID | | paymentType | String | 是 | 支付类型：cash-现金/wechat-微信支付/alipay-支付宝/bank_transfer-银行转账/pos-POS机刷卡/other-其他 | | paymentAmount | Decimal | 是 | 支付金额 | | paymentTime | String | 是 | 支付时间 | | paymentRemark | String | 否 | 支付备注 | | transactionId | String | 否 | 第三方交易号 |

**响应示例**:

```json
{
  "code": 200,
  "message": "收款信息添加成功",
  "data": {
    "paymentId": "payment_001"
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 4.3 修改收款信息

**接口地址**: `POST /publicbiz/domestic-task-orderpayment/update`

**功能说明**: 修改收款信息

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | paymentId | String | 是 | 支付记录ID | | paymentType | String | 否 | 支付类型 | | paymentAmount | Decimal | 否 | 支付金额 | | paymentTime | String | 否 | 支付时间 | | paymentRemark | String | 否 | 支付备注 | | transactionId | String | 否 | 第三方交易号 |

**响应示例**:

```json
{
  "code": 200,
  "message": "收款信息修改成功",
  "data": null,
  "timestamp": "2024-06-15T10:30:00Z"
}
```

## 5. 收支记录管理接口

### 5.1 查询收支记录

**接口地址**: `GET /publicbiz/domestic-task-orderincome-expense/list`

**功能说明**: 查询订单收支记录列表

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | orderId | String | 是 | 订单ID |

**响应字段**: | 字段名 | 类型 | 说明 | |--------|------|------| | records | Array | 收支记录列表 | | records[].id | String | 记录ID | | records[].type | String | 类型：收入/支出 | | records[].amount | Decimal | 金额 | | records[].date | String | 日期 | | records[].description | String | 描述 |

**响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": "record_001",
        "type": "收入",
        "amount": 12800.0,
        "date": "2024-06-01",
        "description": "客户支付服务费"
      },
      {
        "id": "record_002",
        "type": "支出",
        "amount": 8000.0,
        "date": "2024-06-02",
        "description": "支付服务人员工资"
      }
    ]
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 5.2 新增收支记录

**接口地址**: `POST /publicbiz/domestic-task-orderincome-expense/add`

**功能说明**: 新增收支记录

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | orderId | String | 是 | 订单ID | | type | String | 是 | 类型：收入/支出 | | amount | Decimal | 是 | 金额 | | date | String | 是 | 日期 | | description | String | 是 | 描述 |

**响应示例**:

```json
{
  "code": 200,
  "message": "收支记录添加成功",
  "data": {
    "recordId": "record_003"
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 5.3 修改收支记录

**接口地址**: `POST /publicbiz/domestic-task-orderincome-expense/update`

**功能说明**: 修改收支记录

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | recordId | String | 是 | 记录ID | | type | String | 否 | 类型 | | amount | Decimal | 否 | 金额 | | date | String | 否 | 日期 | | description | String | 否 | 描述 |

**响应示例**:

```json
{
  "code": 200,
  "message": "收支记录修改成功",
  "data": null,
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 5.4 删除收支记录

**接口地址**: `POST /publicbiz/domestic-task-orderincome-expense/delete`

**功能说明**: 删除收支记录

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | recordId | String | 是 | 记录ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "收支记录删除成功",
  "data": null,
  "timestamp": "2024-06-15T10:30:00Z"
}
```

## 6. 服务评价管理接口

### 6.1 查询服务评价

**接口地址**: `GET /publicbiz/domestic-task-orderevaluation/info`

**功能说明**: 查询订单服务评价信息

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | orderId | String | 是 | 订单ID |

**响应字段**: | 字段名 | 类型 | 说明 | |--------|------|------| | overallRating | Integer | 总体满意度评分 | | tags | Array | 评价标签列表 | | comment | String | 详细评价 | | evaluationTime | String | 评价时间 |

**响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "overallRating": 5,
    "tags": ["非常专业", "准时到达", "清洁彻底"],
    "comment": "非常满意的一次服务!团队很专业,干活利索,边边角角都处理得很干净,下次有需要还会再来。",
    "evaluationTime": "2024-06-15 16:30:00"
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 6.2 新增服务评价

**接口地址**: `POST /publicbiz/domestic-task-orderevaluation/add`

**功能说明**: 新增服务评价

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | orderId | String | 是 | 订单ID | | overallRating | Integer | 是 | 总体满意度评分(1-5) | | tags | Array | 否 | 评价标签列表 | | comment | String | 否 | 详细评价 | | evaluationTime | String | 是 | 评价时间 |

**响应示例**:

```json
{
  "code": 200,
  "message": "服务评价添加成功",
  "data": {
    "evaluationId": "eval_001"
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 6.3 修改服务评价

**接口地址**: `POST /publicbiz/domestic-task-orderevaluation/update`

**功能说明**: 修改服务评价

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | evaluationId | String | 是 | 评价ID | | overallRating | Integer | 否 | 总体满意度评分 | | tags | Array | 否 | 评价标签列表 | | comment | String | 否 | 详细评价 |

**响应示例**:

```json
{
  "code": 200,
  "message": "服务评价修改成功",
  "data": null,
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 6.4 删除服务评价

**接口地址**: `POST /publicbiz/domestic-task-orderevaluation/delete`

**功能说明**: 删除服务评价

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | evaluationId | String | 是 | 评价ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "服务评价删除成功",
  "data": null,
  "timestamp": "2024-06-15T10:30:00Z"
}
```

## 7. 售后记录管理接口

### 7.1 查询售后记录

**接口地址**: `GET /publicbiz/domestic-task-orderafter-sales/list`

**功能说明**: 查询订单售后记录列表

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | orderId | String | 是 | 订单ID |

**响应字段**: | 字段名 | 类型 | 说明 | |--------|------|------| | records | Array | 售后记录列表 | | records[].id | String | 记录ID | | records[].workOrderType | String | 工单类型 | | records[].problemDescription | String | 问题描述 | | records[].workOrderStatus | String | 工单状态 | | records[].processingResult | String | 处理结果 | | records[].createTime | String | 创建时间 |

**响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": "after_sales_001",
        "workOrderType": "服务质量投诉",
        "problemDescription": "服务人员态度不好，清洁不彻底",
        "workOrderStatus": "processing",
        "processingResult": "已安排重新服务，并更换服务人员",
        "createTime": "2024-06-10 14:30:00"
      }
    ]
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 7.2 新增售后记录

**接口地址**: `POST /publicbiz/domestic-task-orderafter-sales/add`

**功能说明**: 新增售后记录

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | orderId | String | 是 | 订单ID | | workOrderType | String | 是 | 工单类型 | | problemDescription | String | 是 | 问题描述 | | workOrderStatus | String | 是 | 工单状态：pending-待处理/processing-处理中/completed-已完成/cancelled-已取消 | | processingResult | String | 否 | 处理结果 | | createTime | String | 是 | 创建时间 |

**响应示例**:

```json
{
  "code": 200,
  "message": "售后记录添加成功",
  "data": {
    "afterSalesId": "after_sales_002"
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 7.3 修改售后记录

**接口地址**: `POST /publicbiz/domestic-task-orderafter-sales/update`

**功能说明**: 修改售后记录

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | afterSalesId | String | 是 | 售后记录ID | | workOrderType | String | 否 | 工单类型 | | problemDescription | String | 否 | 问题描述 | | workOrderStatus | String | 否 | 工单状态 | | processingResult | String | 否 | 处理结果 |

**响应示例**:

```json
{
  "code": 200,
  "message": "售后记录修改成功",
  "data": null,
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 7.4 删除售后记录

**接口地址**: `POST /publicbiz/domestic-task-orderafter-sales/delete`

**功能说明**: 删除售后记录

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | afterSalesId | String | 是 | 售后记录ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "售后记录删除成功",
  "data": null,
  "timestamp": "2024-06-15T10:30:00Z"
}
```

## 8. 操作日志管理接口

### 8.1 查询操作日志

**接口地址**: `GET /publicbiz/domestic-task-orderlog/list`

**功能说明**: 查询订单操作日志列表

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | orderId | String | 是 | 订单ID | | page | Integer | 是 | 页码 | | size | Integer | 是 | 每页大小 | | logType | String | 否 | 日志类型 |

**响应字段**: | 字段名 | 类型 | 说明 | |--------|------|------| | total | Integer | 总记录数 | | records | Array | 日志列表 | | records[].id | String | 日志ID | | records[].logType | String | 日志类型 | | records[].logTitle | String | 日志标题 | | records[].logContent | String | 日志内容 | | records[].oldStatus | String | 原状态 | | records[].newStatus | String | 新状态 | | records[].operatorName | String | 操作人姓名 | | records[].operatorRole | String | 操作人角色 | | records[].createTime | String | 创建时间 |

**响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 10,
    "records": [
      {
        "id": "log_001",
        "logType": "订单创建",
        "logTitle": "新建家政服务订单",
        "logContent": "创建了订单DS202406001，客户：李女士",
        "oldStatus": "",
        "newStatus": "draft",
        "operatorName": "张三",
        "operatorRole": "客服",
        "createTime": "2024-06-01 09:00:00"
      }
    ]
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

## 9. 基础数据查询接口

### 9.1 获取服务套餐列表

**接口地址**: `GET /publicbiz/domestic-task-orderpackage/list`

**功能说明**: 获取服务套餐列表

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | category | String | 否 | 服务分类 | | status | String | 否 | 状态：active-已上架/pending-待上架 |

**响应字段**: | 字段名 | 类型 | 说明 | |--------|------|------| | packages | Array | 套餐列表 | | packages[].id | String | 套餐ID | | packages[].name | String | 套餐名称 | | packages[].category | String | 服务分类 | | packages[].price | Decimal | 套餐价格 | | packages[].originalPrice | Decimal | 原价 | | packages[].unit | String | 价格单位 | | packages[].serviceDuration | String | 服务时长 | | packages[].status | String | 状态 |

**响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "packages": [
      {
        "id": "package_a",
        "name": "SP001 - 月嫂服务套餐",
        "category": "月嫂服务",
        "price": 12800.0,
        "originalPrice": 13800.0,
        "unit": "月",
        "serviceDuration": "26天",
        "status": "active"
      }
    ]
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 9.2 获取服务机构列表

**接口地址**: `GET /publicbiz/domestic-task-orderagency/list`

**功能说明**: 获取服务机构列表

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | agencyType | String | 否 | 机构类型 | | cooperationStatus | String | 否 | 合作状态 | | keyword | String | 否 | 搜索关键词 |

**响应字段**: | 字段名 | 类型 | 说明 | |--------|------|------| | agencies | Array | 机构列表 | | agencies[].id | Long | 机构ID | | agencies[].agencyName | String | 机构名称 | | agencies[].agencyType | String | 机构类型 | | agencies[].cooperationStatus | String | 合作状态 | | agencies[].contactPerson | String | 联系人 | | agencies[].contactPhone | String | 联系电话 |

**响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "agencies": [
      {
        "id": 1001,
        "agencyName": "爱家月嫂服务中心",
        "agencyType": "cooperation",
        "cooperationStatus": "cooperating",
        "contactPerson": "李经理",
        "contactPhone": "13812345678"
      }
    ]
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 9.3 获取服务人员列表

**接口地址**: `GET /publicbiz/domestic-task-orderpractitioner/list`

**功能说明**: 获取服务人员列表

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | serviceType | String | 否 | 服务类型 | | agencyId | Long | 否 | 机构ID | | status | String | 否 | 状态 | | keyword | String | 否 | 搜索关键词 |

**响应字段**: | 字段名 | 类型 | 说明 | |--------|------|------| | practitioners | Array | 服务人员列表 | | practitioners[].auntOneid | String | 阿姨OneID | | practitioners[].name | String | 姓名 | | practitioners[].phone | String | 电话 | | practitioners[].serviceType | String | 服务类型 | | practitioners[].experienceYears | Integer | 从业年限 | | practitioners[].rating | Decimal | 评级 | | practitioners[].agencyName | String | 所属机构 | | practitioners[].currentStatus | String | 当前状态 |

**响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "practitioners": [
      {
        "auntOneid": "practitioner_001",
        "name": "张阿姨",
        "phone": "13987654321",
        "serviceType": "月嫂",
        "experienceYears": 5,
        "rating": 4.8,
        "agencyName": "爱家月嫂服务中心",
        "currentStatus": "待岗"
      }
    ]
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

### 9.4 获取客户基本信息

**接口地址**: `GET /publicbiz/domestic-task-ordercustomer/info`

**功能说明**: 获取客户基本信息

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | customerOneid | String | 是 | 客户OneID |

**响应字段**: | 字段名 | 类型 | 说明 | |--------|------|------| | customerOneid | String | 客户OneID | | customerName | String | 客户姓名 | | customerPhone | String | 客户电话 | | customerAddress | String | 客户地址 | | totalOrders | Integer | 总订单数 | | totalAmount | Decimal | 总消费金额 |

**响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "customerOneid": "customer_001",
    "customerName": "李女士",
    "customerPhone": "13812345678",
    "customerAddress": "北京市朝阳区建国路88号",
    "totalOrders": 5,
    "totalAmount": 45000.0
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

## 10. 人员变动管理接口

### 10.1 查询人员变动记录

**接口地址**: `GET /publicbiz/domestic-task-orderpersonnel-change/list`

**功能说明**: 查询订单人员变动记录

**请求参数**: | 参数名 | 类型 | 必填 | 说明 | |--------|------|------|------| | orderId | String | 是 | 订单ID |

**响应字段**: | 字段名 | 类型 | 说明 | |--------|------|------| | changes | Array | 变动记录列表 | | changes[].id | String | 记录ID | | changes[].changeType | String | 变动类型 | | changes[].oldPersonnel | String | 原服务人员 | | changes[].newPersonnel | String | 新服务人员 | | changes[].changeReason | String | 变动原因 | | changes[].changeTime | String | 变动时间 | | changes[].operator | String | 操作人 |

**响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "changes": [
      {
        "id": "change_001",
        "changeType": "服务人员更换",
        "oldPersonnel": "张阿姨",
        "newPersonnel": "李阿姨",
        "changeReason": "原服务人员身体不适",
        "changeTime": "2024-06-05 10:00:00",
        "operator": "王经理"
      }
    ]
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

## 11. 统计信息接口

### 11.1 获取订单统计信息

**接口地址**: `GET /publicbiz/domestic-task-orderstatistics`

**功能说明**: 获取订单统计信息

**请求参数**: 无

**响应字段**: | 字段名 | 类型 | 说明 | |--------|------|------| | totalOrders | Integer | 总订单数 | | pendingOrders | Integer | 待处理订单数 | | monthlyAmount | Decimal | 本月订单金额 | | completionRate | Decimal | 订单完成率 |

**响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "totalOrders": 18,
    "pendingOrders": 3,
    "monthlyAmount": 2135220.0,
    "completionRate": 92.5
  },
  "timestamp": "2024-06-15T10:30:00Z"
}
```

## 12. 数据字典

### 12.1 订单状态枚举

| 状态值     | 说明   |
| ---------- | ------ |
| pending    | 待派单 |
| assigned   | 已派单 |
| in_service | 服务中 |
| completed  | 已完成 |
| cancelled  | 已取消 |

### 12.2 支付状态枚举

| 状态值  | 说明     |
| ------- | -------- |
| unpaid  | 未支付   |
| paid    | 已支付   |
| partial | 部分支付 |

### 12.3 服务类型枚举

| 类型值              | 说明       |
| ------------------- | ---------- |
| maternity           | 月嫂服务   |
| deep_cleaning       | 深度保洁   |
| hourly              | 小时工     |
| nanny               | 育儿嫂服务 |
| range_hood_cleaning | 油烟机清洗 |
| daily_cleaning      | 日常保洁   |
| glass_cleaning      | 玻璃清洗   |

### 12.4 任务状态枚举

| 状态值      | 说明   |
| ----------- | ------ |
| pending     | 待指派 |
| assigned    | 已分配 |
| in_progress | 进行中 |
| completed   | 已完成 |
| cancelled   | 已取消 |

### 12.5 支付类型枚举

| 类型值        | 说明      |
| ------------- | --------- |
| cash          | 现金      |
| wechat        | 微信支付  |
| alipay        | 支付宝    |
| bank_transfer | 银行转账  |
| pos           | POS机刷卡 |
| other         | 其他      |

### 12.6 机构类型枚举

| 类型值      | 说明     |
| ----------- | -------- |
| cooperation | 合作     |
| competitor  | 竞争对手 |
| other       | 其他     |

### 12.7 合作状态枚举

| 状态值      | 说明   |
| ----------- | ------ |
| cooperating | 合作中 |
| suspended   | 已暂停 |
| terminated  | 已终止 |

### 12.8 日志类型枚举

| 类型值     | 说明       |
| ---------- | ---------- |
| 订单创建   | 订单创建   |
| 编辑       | 编辑       |
| 审批通过   | 审批通过   |
| 审批驳回   | 审批驳回   |
| 确认收款   | 确认收款   |
| 完成       | 完成       |
| 系统管理员 | 系统管理员 |

## 13. 错误码详细说明

### 13.1 业务错误码

| 错误码 | 说明               | 解决方案                  |
| ------ | ------------------ | ------------------------- |
| 40001  | 订单不存在         | 检查订单ID是否正确        |
| 40002  | 订单状态不允许操作 | 检查订单当前状态          |
| 40003  | 服务人员不存在     | 检查服务人员OneID是否正确 |
| 40004  | 服务机构不存在     | 检查机构ID是否正确        |
| 40005  | 任务不存在         | 检查任务ID是否正确        |
| 40006  | 任务状态不允许操作 | 检查任务当前状态          |
| 40007  | 支付记录不存在     | 检查支付记录ID是否正确    |
| 40008  | 评价记录不存在     | 检查评价ID是否正确        |
| 40009  | 售后记录不存在     | 检查售后记录ID是否正确    |
| 40010  | 收支记录不存在     | 检查收支记录ID是否正确    |

### 13.2 参数错误码

| 错误码 | 说明           | 解决方案                     |
| ------ | -------------- | ---------------------------- |
| 40011  | 必填参数缺失   | 检查必填参数是否完整         |
| 40012  | 参数格式错误   | 检查参数格式是否正确         |
| 40013  | 参数值超出范围 | 检查参数值是否在允许范围内   |
| 40014  | 日期格式错误   | 检查日期格式是否为YYYY-MM-DD |
| 40015  | 金额格式错误   | 检查金额格式是否正确         |

### 13.3 权限错误码

| 错误码 | 说明             | 解决方案     |
| ------ | ---------------- | ------------ |
| 40016  | 无权限操作此订单 | 检查用户权限 |
| 40017  | 无权限查看此订单 | 检查用户权限 |
| 40018  | 无权限删除此记录 | 检查用户权限 |

### 13.4 系统错误码

| 错误码 | 说明               | 解决方案           |
| ------ | ------------------ | ------------------ |
| 50001  | 数据库连接失败     | 联系系统管理员     |
| 50002  | 文件上传失败       | 检查文件格式和大小 |
| 50003  | 第三方服务调用失败 | 稍后重试           |
| 50004  | 系统内部错误       | 联系系统管理员     |

## 14. 接口调用示例

### 14.1 完整订单创建流程

```bash
# 1. 获取服务套餐列表
GET /publicbiz/domestic-task-orderpackage/list

# 2. 获取服务机构列表
GET /publicbiz/domestic-task-orderagency/list

# 3. 获取服务人员列表
GET /publicbiz/domestic-task-orderpractitioner/list

# 4. 创建订单
POST /publicbiz/domestic-task-orderadd
{
  "customerName": "王女士",
  "customerPhone": "13987654321",
  "serviceAddress": "北京市朝阳区建国路88号",
  "servicePackage": "package_a",
  "serviceType": "maternity",
  "serviceStartDate": "2024-07-01",
  "serviceEndDate": "2024-07-31",
  "unitPrice": 12800.00,
  "totalAmount": 12800.00,
  "actualAmount": 12800.00,
  "practitionerOneid": "practitioner_001",
  "agencyId": 1001
}

# 5. 查询订单详情
GET /publicbiz/domestic-task-orderdetail?id=1001
```

### 14.2 任务管理流程

```bash
# 1. 查询任务列表
GET /publicbiz/domestic-task-ordertask/page?orderId=1001&page=1&size=10

# 2. 指派任务
POST /publicbiz/domestic-task-ordertask/assign
{
  "taskId": "task_001",
  "practitionerOneid": "practitioner_001",
  "practitionerName": "张阿姨",
  "practitionerPhone": "13987654321",
  "plannedStartTime": "2024-07-01 09:00:00",
  "plannedEndTime": "2024-07-01 18:00:00"
}

# 3. 完成任务
POST /publicbiz/domestic-task-ordertask/complete
{
  "taskId": "task_001",
  "finalPersonnel": "张阿姨",
  "completionTime": "2024-07-01 18:00:00",
  "punchLocation": "北京市朝阳区建国路88号",
  "certificateFile": "file",
  "remarks": "服务完成，客户满意"
}
```

### 14.3 收款管理流程

```bash
# 1. 查询收款信息
GET /publicbiz/domestic-task-orderpayment/info?orderId=1001

# 2. 新增收款记录
POST /publicbiz/domestic-task-orderpayment/add
{
  "orderId": "1001",
  "paymentType": "bank_transfer",
  "paymentAmount": 12800.00,
  "paymentTime": "2024-07-01 10:00:00",
  "paymentRemark": "客户通过银行转账支付",
  "transactionId": "TX20240701001"
}

# 3. 查询收支记录
GET /publicbiz/domestic-task-orderincome-expense/list?orderId=1001
```

## 15. 注意事项

### 15.1 接口调用规范

1. 所有请求必须包含有效的认证Token
2. 请求参数中的日期格式统一使用YYYY-MM-DD或YYYY-MM-DD HH:mm:ss
3. 金额字段统一使用Decimal类型，保留2位小数
4. 文件上传接口支持的最大文件大小为10MB
5. 分页查询接口的每页大小最大为100条记录

### 15.2 数据安全

1. 敏感信息（如身份证号、手机号）在传输过程中需要加密
2. 文件上传需要验证文件类型和大小
3. 所有操作都会记录操作日志
4. 删除操作采用软删除，不会物理删除数据

### 15.3 性能优化

1. 分页查询建议使用合理的页面大小
2. 大量数据导出建议使用异步处理
3. 文件上传建议使用分片上传
4. 缓存机制可以提高查询性能

### 15.4 错误处理

1. 客户端需要处理所有可能的错误码
2. 网络异常时需要实现重试机制
3. 用户友好的错误提示信息
4. 关键操作需要二次确认

## 16. 更新日志

### v1.0.0 (2024-06-15)

- 初始版本发布
- 包含完整的订单管理功能
- 支持任务管理、收款管理、评价管理等功能
- 提供完整的API接口文档

---

**文档版本**: v1.0.0  
**最后更新**: 2024-06-15  
**维护人员**: 开发团队  
**联系方式**: <EMAIL>
