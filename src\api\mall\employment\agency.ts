import request from '@/config/axios'

/**
 * 机构管理 API（按接口文档 /publicbiz/agency/...）
 */

// 列表查询参数（对接接口文档命名）
export interface AgencyPageParams {
  pageNum: number
  pageSize: number
  keyword?: string
  cooperationStatus?: 'cooperating' | 'suspended' | 'terminated' | 'pending'
  reviewStatus?: 'pending' | 'approved' | 'rejected'
  district?: string
  agencyNo?: string
}

// 机构详情（保持与文档字段一致，便于直接展示）
export interface AgencyDetailVO {
  id: number
  agencyName: string
  agencyShortName?: string
  agencyNo: string
  agencyType?: string
  legalRepresentative?: string
  unifiedSocialCreditCode?: string
  establishmentDate?: string
  registeredAddress?: string
  operatingAddress?: string
  businessScope?: string
  applicantName?: string
  applicantPhone?: string
  applicationTime?: string
  contactPerson?: string
  contactPhone?: string
  contactEmail?: string
  province?: string
  city?: string
  district?: string
  cooperationStatus?: 'cooperating' | 'suspended' | 'terminated' | 'pending'
  reviewStatus?: 'pending' | 'approved' | 'rejected'
  qualifications?: Array<{
    fileType: string
    fileName: string
    fileUrl: string
  }>
}

// 审核更新参数
export interface AgencyReviewUpdateParams {
  id: number
  reviewStatus: 'approved' | 'rejected'
  reviewRemark?: string
}

// 分页返回的通用结构（后端常见返回 { list, total }）
export interface PageResult<T> {
  list: T[]
  total: number
  pageNum?: number
  pageSize?: number
}

// 获取机构分页
export function getAgencyPage(params: AgencyPageParams) {
  return request.get<PageResult<AgencyDetailVO> | AgencyDetailVO[]>({
    url: '/publicbiz/agency/page',
    params
  })
}

// 获取机构列表（不分页）
export function getAgencyListNoPaging(params?: {
  keyword?: string
  cooperationStatus?: 'cooperating' | 'suspended' | 'terminated' | 'pending'
  reviewStatus?: 'pending' | 'approved' | 'rejected'
  district?: string
  agencyNo?: string
}) {
  return request.get<AgencyDetailVO[]>({
    url: '/publicbiz/agency/list',
    params
  })
}

// 获取机构详情
export function getAgencyDetail(id: number) {
  return request.get<AgencyDetailVO>({
    url: `/publicbiz/agency/get/${id}`
  })
}

// 审核更新
export function updateAgencyReview(data: AgencyReviewUpdateParams) {
  return request.put<void>({
    url: '/publicbiz/agency/update',
    data
  })
}

// 兼容旧方法名（如代码中曾经引用）
export const getAgencyList = getAgencyPage

// 机构业务统计数据接口参数
export interface AgencyStatisticsParams {
  agencyId: number
  rangeType?: '30' | '90' | 'year' | 'all'
}

// 机构业务统计数据响应
export interface AgencyStatisticsVO {
  serviceOrderCount: number
  interviewSuccessRate: number
  agencyRating: number
  complaintRate: number
  totalPractitioners: number
  newPractitioners: number
  flowPractitioners: number
  activeOrderPractitioners: number
  totalOrderAmount: number
  ourIncome: number
  settledAmount: number
  unsettledAmount: number
}

// 获取机构业务统计数据
export function getAgencyStatistics(params: AgencyStatisticsParams) {
  return request.get<AgencyStatisticsVO>({
    url: '/publicbiz/agency/statistics',
    params
  })
}

// 机构业务趋势数据响应
export interface AgencyTrendVO {
  orderTrends: Array<{ month: string; count: number }>
  serviceCategories: Array<{ serviceType: string; percentage: number }>
  serviceQualities: Array<{ month: string; score: number }>
}

// 获取机构业务趋势数据
export function getAgencyTrend(agencyId: number) {
  return request.get<AgencyTrendVO>({
    url: `/publicbiz/agency/trend/${agencyId}`
  })
}

// ==================== 机构资质文件管理接口 ====================

// 机构资质文件类型定义
export interface AgencyQualificationFile {
  agencyId: number
  fileType:
    | 'business_license'
    | 'qualification_cert'
    | 'account_opening_permit'
    | 'contract'
    | 'other'
    | 'human_resources'
    | 'door_photo'
    | 'organizational_structure'
    | 'id_card_front'
    | 'id_card_back'
  fileCategory: string
  fileDescription: string
  fileName: string
  fileUrl: string
  fileSize: number
  fileExtension: string
  sortOrder: number
}

// 获取机构资质文件列表参数
export interface AgencyQualificationListParams {
  agencyId: number
  fileType?: Array<
    | 'business_license'
    | 'qualification_cert'
    | 'account_opening_permit'
    | 'contract'
    | 'other'
    | 'human_resources'
    | 'door_photo'
    | 'organizational_structure'
    | 'id_card_front'
    | 'id_card_back'
  >
}

// 获取机构资质文件列表
export function getAgencyQualificationList(params: AgencyQualificationListParams) {
  return request.get<AgencyQualificationFile[]>({
    url: '/publicbiz/agency/qualification/list',
    params
  })
}

// 上传机构资质文件参数
export interface UploadAgencyQualificationParams {
  agencyId: number
  fileType:
    | 'business_license'
    | 'qualification_cert'
    | 'account_opening_permit'
    | 'contract'
    | 'other'
    | 'human_resources'
    | 'door_photo'
    | 'organizational_structure'
    | 'id_card_front'
    | 'id_card_back'
  fileCategory?: string
  fileDescription?: string
  fileName: string
  fileUrl: string
  fileSize?: number
  fileExtension?: string
  sortOrder?: number
}

// 上传机构资质文件
export function uploadAgencyQualification(data: UploadAgencyQualificationParams) {
  return request.post<{ id: number }>({
    url: '/publicbiz/agency/qualification/upload',
    data
  })
}

// 删除机构资质文件
export function deleteAgencyQualification(agencyId: number, fileType: string) {
  return request.delete<void>({
    url: `/publicbiz/agency/qualification/delete`,
    params: { agencyId, fileType }
  })
}
